'use client';

import { useTRPC } from "~/trpc/react";
import PropertyTypesDataTable from "./property-types-data-table";
import PropertyTypeFormSheet from "./property-type-form-sheet";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ property_type_id?: string }>;

const PropertyTypesPage = () => {
  const trpc = useTRPC();
  const { data: propertyTypes } = useQuery(trpc.property.getAllPropertyType.queryOptions());

  // if (searchParams.property_type_id) {
  //   await api.property.getPropertyTypeById.prefetch({
  //     propertyTypeId: searchParams.property_type_id,
  //   });
  // }

  if (!propertyTypes) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Property Types
      </h1>

      <PropertyTypeFormSheet />
      <PropertyTypesDataTable data={propertyTypes} />
    </>
  );
};

export default PropertyTypesPage;
