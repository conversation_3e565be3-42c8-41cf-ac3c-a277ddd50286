"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";

import AddUpdatePropertyTypeSchema from "~/server/api/validations/add-update-property-type.validation";
import { useTRPC } from "~/trpc/react";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { skipToken, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSheet } from "~/app/hooks/use-sheet";
import { PropertyTypeParamName } from "~/app/helpers/constants";

const PropertyTypeAddUpdateForm = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { paramValue: propertyTypeId, closeSheet } = useSheet(
    PropertyTypeParamName,
  );

  const {
    data: propertyTypeDetail,
    isLoading,
    error,
  } = useQuery(trpc.property.getPropertyTypeById.queryOptions(
    propertyTypeId ? { propertyTypeId } : skipToken,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  ));

  const { mutate: updatePropertyType, isPending: isPendingUpdateMutation } =
    useMutation(trpc.property.updatePropertyType.mutationOptions());
  const { mutate: addPropertyType, isPending: isPendingAddMutation } =
    useMutation(trpc.property.addPropertyType.mutationOptions());
  const { data: propertyCategories } =
    useQuery(trpc.propertyCategories.getAllCategories.queryOptions());

  const form = useForm<z.infer<typeof AddUpdatePropertyTypeSchema>>({
    resolver: zodResolver(AddUpdatePropertyTypeSchema),
    defaultValues: propertyTypeDetail
      ? {
          name: propertyTypeDetail.name,
          category: propertyTypeDetail.category?.id,
        }
      : {
          name: "",
          category: "",
        },
  });

  const onSubmit = (values: z.infer<typeof AddUpdatePropertyTypeSchema>) => {
    if (propertyTypeId) {
      updatePropertyType(
        { ...values, id: propertyTypeId },
        {
          onSuccess: (opts) => {
            if (opts.warning) {
              toast.error(opts.message);
              return;
            }
            toast.success(opts.message);
            void queryClient.invalidateQueries({
              queryKey: [
                trpc.property.getPropertyTypeById.queryKey({
                  propertyTypeId,
                }),
                trpc.property.getAllPropertyType.queryKey(),
              ],
            });
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      addPropertyType(values, {
        onSuccess: (opts) => {
          if (opts.warning) {
            toast.error(opts.message);
            return;
          }
          toast.success(opts.message);
          void queryClient.invalidateQueries({
            queryKey: trpc.property.getAllPropertyType.queryKey(),
          });
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  if (isLoading && propertyTypeId) {
    return (
      <div className="p-4 text-center">Loading property type details...</div>
    );
  }

  if (error && propertyTypeId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading property type: {error.message}
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-4">
        <FormField
          control={form.control}
          name="category"
          render={({ field }) => {
            const selectedCategory = propertyCategories?.find(
              (item) => item.id === field.value,
            );

            return (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          selectedCategory?.name ?? "Select a category"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {propertyCategories?.map((item) => (
                        <SelectItem key={item.id} value={item.id}>
                          {item.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Property Type Name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {propertyTypeId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          propertyTypeId && (
            <Button type="submit" className="w-full py-3">
              Update
            </Button>
          )
        )}

        {!propertyTypeId && isPendingAddMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !propertyTypeId && (
            <Button type="submit" className="w-full py-3">
              Create
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default PropertyTypeAddUpdateForm;
