'use client';

import React from "react";
import UserSelection from "./user-selection";

import { useTRPC } from "~/trpc/react";
import PostsGrid from "./posts-grid";
import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ user_id?: string }>;

const Posts = () => {
  const trpc = useTRPC();
  const searchParams = useSearchParams();
  const { data: partners } = useQuery(trpc.partner.getAllPartners.queryOptions());

  // if (searchParams.user_id) {
  //   await api.post.getPostsByUserId.prefetch({ userId: searchParams.user_id });
  // }

  if (!partners) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">Posts</h1>

      <UserSelection partners={partners} />

      <PostsGrid userId={searchParams.get("user_id") ?? undefined} />
    </>
  );
};

export default Posts;
