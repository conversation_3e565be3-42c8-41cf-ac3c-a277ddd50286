"use client";

import PostCard from "@repo/ui/components/shared/post-card";
import { skipToken, useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import React from "react";
import { useTRPC } from "~/trpc/react";

const PostsGrid = ({ userId }: { userId?: string }) => {
  const trpc = useTRPC();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: posts } = useQuery(trpc.post.getPostsByUserId.queryOptions(
    userId ? { userId: userId } : skipToken,
  ));

  const handlePostClick = (postId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("postId", postId);
    router.replace(`?${params}`, { scroll: false });
  };

  if (!posts || posts.length === 0) {
    return <div className="py-4 text-xl font-medium">No posts found</div>;
  }

  // Note: No need to show any loading state becoz i am prefetching the data in server component, so all the posts will be available in the client component instantly

  return (
    <div className="grid grid-cols-1 gap-5 py-4 md:grid-cols-2 lg:grid-cols-3">
      {posts.map((post) => (
        <div key={post.id} className="aspect-video overflow-auto">
          <PostCard onPostClick={handlePostClick} {...post} />
        </div>
      ))}
    </div>
  );
};

export default PostsGrid;
