'use client';

import { useTRPC } from "~/trpc/react";
import PropertiesDataTable from "./properties-data-table";
import PostPropertyFormSheet from "../partners/post-property/post-property-form-sheet";
import type { PropertyStatusEnum } from "@repo/database";
import PropertyStatusTabs from "./property-status-tabs";
import PropertyCardView from "./property-card-view";
import CardsSkeleton from "~/app/components/shared/card-skeleton";
import TableSkeleton from "~/app/components/shared/table-skeleton";
import PaginationButtons from "../partners/pagination-buttons";
import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{
//   status?: string;
//   view?: "table" | "cards";
//   page?: number;
//   take?: number;
// }>;

const PropertiesPage = () => {
  const searchParams = useSearchParams();

  const status = (searchParams.get('status') ?? "ACTIVE") as PropertyStatusEnum;
  const view = searchParams.get('view') === "cards" ? "cards" : "table";
  const currentPage = Number(searchParams.get('page') ?? 1);

  // await api.property.getStatus.prefetch();

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Properties
      </h1>

      <PropertyStatusTabs activeStatus={status} view={view} />

      {/* <Suspense
        fallback={view === "cards" ? <CardsSkeleton /> : <TableSkeleton />}
      > */}
        <PropertyDataView
          status={status}
          view={view}
          currentPage={currentPage}
        />
      {/* </Suspense> */}

      <PostPropertyFormSheet />
    </>
  );
};

export default PropertiesPage;

const PropertyDataView = ({
  status,
  view,
  currentPage,
}: {
  status: PropertyStatusEnum;
  view: "table" | "cards";
  currentPage?: number;
}) => {
  const trpc = useTRPC();
  const { data: properties } = useQuery(trpc.property.getAllProperties.queryOptions({
    propertyStatus: status,
    page: view === "cards" ? currentPage : undefined,
    take: view === "cards" ? 9 : undefined,
  }));

  if (!properties) {
    return null;
  }

  return (
    <>
      {view === "cards" && (
        <div className="mb-4 flex justify-end">
          <PaginationButtons
            currentPage={currentPage ?? 1}
            totalPages={properties.totalPages}
            status={status}
            view={view}
          />
        </div>
      )}

      {view === "cards" ? (
        <PropertyCardView properties={properties.properties} />
      ) : (
        <PropertiesDataTable
          data={properties.properties}
          currentStatus={status}
        />
      )}
    </>
  );
};
