"use client";

import { useCallback } from "react";
import Link from "next/link";
import { cn } from "@repo/ui/lib/utils";
import { useTRPC } from "~/trpc/react";
import { Card } from "@repo/ui/components/ui/card";
import { PropertyStatusEnum } from "@repo/database";
import ViewSelectionDropdown from "./view-selection-dropdown";
import { useQuery } from "@tanstack/react-query";

export interface PropertyStatusTabsProps {
  activeStatus: string;
  view: "table" | "cards";
}

const PropertyStatusTabs = ({
  activeStatus,
  view,
}: PropertyStatusTabsProps) => {
  const trpc = useTRPC();
  const { data: statusCounts, isLoading } = useQuery(trpc.property.getStatus.queryOptions());

  const countByStatus = useCallback(
    (status: PropertyStatusEnum | "ALL") => {
      if (isLoading || !statusCounts) return 0;
      if (status === "ALL") {
        return statusCounts.length;
      }
      return statusCounts.filter((p) => p.propertyStatus === status).length;
    },
    [statusCounts, isLoading],
  );

  return (
    <div className="flex flex-wrap items-center justify-between gap-x-10 space-y-6">
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-7">
        {!isLoading &&
          statusCounts &&
          Object.entries(PropertyStatusEnum).map(
            ([value, label]) =>
              value !== "ALL" && (
                <Link
                  key={value}
                  href={`/properties?status=${value}&view=${view}`}
                  className="h-full"
                >
                  <Card
                    className={cn(
                      "flex h-full flex-col p-4",
                      activeStatus === value &&
                        "border-black bg-secondary-2-100",
                    )}
                  >
                    <h3 className="text-xl font-medium capitalize">
                      {label.toLowerCase()} Properties
                    </h3>
                    <p className="mt-auto pt-2 text-2xl font-medium text-secondary-2-700">
                      {countByStatus(value as PropertyStatusEnum)}
                    </p>
                  </Card>
                </Link>
              ),
          )}
      </div>

      <ViewSelectionDropdown currentView={view} currentStatus={activeStatus} />
    </div>
  );
};

export default PropertyStatusTabs;
