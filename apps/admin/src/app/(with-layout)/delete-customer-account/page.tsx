'use client';
import { useTRPC } from "~/trpc/react";
import {
  DeleteCustomerAccountDataTable,
  deleteCustomerAccountRequestsColumns,
} from "./delete-customer-account-data-table";
import { useQuery } from "@tanstack/react-query";
// import {
//   DeleteAccountDataTable,
//   deleteAccountRequestsColumns,
// } from "./delete-account-data-table";

const DeleteAccount = () => {
  const trpc = useTRPC();
  const { data: deleteCustomerAccountRequests } =
    useQuery(trpc.deleteAccounts.getDeleteCustomerAccountReq.queryOptions());

  if (!deleteCustomerAccountRequests) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Delete Accounts
      </h1>

      <DeleteCustomerAccountDataTable
        columns={deleteCustomerAccountRequestsColumns}
        data={deleteCustomerAccountRequests}
      />
    </>
  );
};

export default DeleteAccount;
