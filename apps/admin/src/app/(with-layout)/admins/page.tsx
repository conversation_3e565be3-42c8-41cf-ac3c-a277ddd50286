'use client'
import { useTR<PERSON> } from "~/trpc/react";
import AdminFormSheet from "./admin-form-sheet";
import { adminColumns, AdminDataTable } from "./admin-data-table";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ admin_id?: string }>;

const AdminsPage = () => {
  const trpc = useTRPC();
  // const searchParams = await props.searchParams;
  // const queryClient = getQueryClient();
  const { data: admins } = useQuery(trpc.admin.getAllAdmins.queryOptions());

  // if (searchParams.admin_id) {
  //   await queryClient.prefetchQuery(trpc.admin.getAdminById.queryOptions({ id: searchParams.admin_id }));
  // }

  if (!admins) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">Admins</h1>

      <AdminFormSheet />
      <AdminDataTable data={admins} columns={adminColumns} />
    </>
  );
};

export default AdminsPage;
