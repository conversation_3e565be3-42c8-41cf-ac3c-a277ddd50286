'use client';

import { useTR<PERSON> } from "~/trpc/react";
import NewsFormSheet from "./news-form-sheet";
import NewsDataTable, { newsDataTableColumns } from "./news-data-table";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ news_id?: string }>;

const NewsPage = () => {
  const trpc = useTRPC();
  // const searchParams = await props.searchParams;
  const { data: news } = useQuery(trpc.news.getAllNews.queryOptions());

  // const news = await api.news.getAllNews();

  // if (searchParams.news_id) {
  //   await api.news.getNewsById.prefetch({ id: searchParams.news_id });
  // }

  if (!news) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">News</h1>

      <NewsFormSheet />
      <NewsDataTable data={news} columns={newsDataTableColumns} />
    </>
  );
};

export default NewsPage;
