"use client";

import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { <PERSON><PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import { useTRPC } from "~/trpc/react";
import Image from "next/image";
import Loading from "~/app/components/loading";
import { skipToken, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import AddUpdateNewsSchema from "~/server/api/validations/add-update-news.validations";
import { useSheet } from "~/app/hooks/use-sheet";
import { NewsParamName } from "~/app/helpers/constants";
import { ImageIcon } from "lucide-react";

const NewsAddUpdateForm = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { paramValue: newsId, closeSheet } = useSheet(NewsParamName);
  const [isUploadingImage, setIsUploadingImage] = useState<boolean>(false);

  const {
    data: newsDetail,
    isLoading,
    error,
  } = useQuery(trpc.news.getNewsById.queryOptions(newsId ? { id: newsId } : skipToken, {
    staleTime: 1000 * 60 * 5, // 5 minutes
  }));

  const { mutate: updateNews, isPending: isPendingUpdateMutation } =
    useMutation(trpc.news.updateNews.mutationOptions());
  const { mutate: addNews, isPending: isPendingAddMutation } =
    useMutation(trpc.news.addNews.mutationOptions());
  const awsPresignedUrlMutation = useMutation(trpc.aws.getPresignedUrl.mutationOptions());
  const awsGetPublicUrlMutation = useMutation(trpc.aws.getPublicFileUrl.mutationOptions());

  const form = useForm<z.infer<typeof AddUpdateNewsSchema>>({
    resolver: zodResolver(AddUpdateNewsSchema),
    defaultValues: newsDetail
      ? {
          title: newsDetail.title,
          description: newsDetail.description,
          fileKey: newsDetail.fileKey,
          filePublicUrl: newsDetail.filePublicUrl,
          redirectUrl: newsDetail.redirectUrl,
        }
      : {
          title: "",
          description: "",
          fileKey: "",
          filePublicUrl: "",
          redirectUrl: "",
        },
  });

  const onSubmit = (values: z.infer<typeof AddUpdateNewsSchema>) => {
    if (newsId) {
      updateNews(
        { ...values, id: newsId },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            void queryClient.invalidateQueries({
              queryKey: [
                trpc.news.getNewsById.queryKey({ id: newsId }),
                trpc.news.getAllNews.queryKey(),
              ],
            });
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      addNews(values, {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void queryClient.invalidateQueries({
            queryKey: trpc.news.getAllNews.queryKey(),
          });
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  const uploadImage = async (file: File | undefined) => {
    if (!file) return;

    // Validate file type
    const validImageTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    if (!validImageTypes.includes(file.type)) {
      toast.error("Please upload a valid image file (JPEG, PNG, GIF, or WEBP)");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image size should be less than 5MB");
      return;
    }

    try {
      setIsUploadingImage(true);
      const key = `/news/${Date.now()}-${file.name}`;
      const { url, key: presignedUrlKey } =
        await awsPresignedUrlMutation.mutateAsync({
          fileName: key,
          contentType: file.type,
          publicAvailable: true,
        });

      if (!url) {
        setIsUploadingImage(false);
        toast.error("Error uploading media");
        return;
      }

      const requestOptions = {
        method: "PUT",
        body: file,
      };
      await fetch(url, requestOptions);

      const fileUrl = await awsGetPublicUrlMutation.mutateAsync({
        fileKey: presignedUrlKey,
      });

      form.setValue("fileKey", presignedUrlKey);
      form.setValue("filePublicUrl", fileUrl);
      toast.success("Image uploaded successfully");
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Error uploading image");
    } finally {
      setIsUploadingImage(false);
    }
  };

  const removeImage = () => {
    form.setValue("fileKey", "");
    form.setValue("filePublicUrl", "");
    toast.success("Image removed");
  };

  if (isLoading && newsId) {
    return <div className="p-4 text-center">Loading news details...</div>;
  }

  if (error && newsId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading news: {error.message}
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-4">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder="News title" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Input placeholder="News description" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="redirectUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>News URL</FormLabel>
              <FormControl>
                <Input placeholder="News URL..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="filePublicUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>News Image</FormLabel>
              <div className="space-y-4">
                {field.value ? (
                  <div className="flex flex-col items-center space-y-2">
                    <div className="relative h-40 w-40 overflow-hidden rounded-md border">
                      <Image
                        src={field.value}
                        alt="News image"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          document.getElementById("image-upload")?.click()
                        }
                      >
                        Change
                      </Button>
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={removeImage}
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center space-y-3 rounded-md border border-dashed p-6">
                    {isUploadingImage ? (
                      <div className="flex flex-col items-center gap-2">
                        <Loading />
                        <span className="text-sm font-medium">
                          Uploading image...
                        </span>
                      </div>
                    ) : (
                      <>
                        <ImageIcon className="h-8 w-8 text-gray-400" />
                        <div className="space-y-1 text-center">
                          <p className="text-sm font-medium">Upload an image</p>
                          <p className="text-xs text-gray-500">
                            PNG, JPG, GIF, WEBP (Max: 5MB)
                          </p>
                        </div>
                        <Button
                          type="button"
                          variant="secondary"
                          size="sm"
                          onClick={() =>
                            document.getElementById("image-upload")?.click()
                          }
                        >
                          Select Image
                        </Button>
                      </>
                    )}
                  </div>
                )}
                <Input
                  id="image-upload"
                  type="file"
                  className="hidden"
                  accept="image/jpeg,image/png,image/gif,image/webp"
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      await uploadImage(file);
                    }
                  }}
                />
                <FormMessage />
              </div>
            </FormItem>
          )}
        />
        {newsId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          newsId && (
            <Button type="submit" className="w-full py-3">
              Update
            </Button>
          )
        )}
        {!newsId && isPendingAddMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !newsId && (
            <Button type="submit" className="w-full py-3">
              Create
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default NewsAddUpdateForm;
