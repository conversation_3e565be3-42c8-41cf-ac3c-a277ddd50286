'use client';
import { useTRPC } from "~/trpc/react";
import {
  DeleteAccountDataTable,
  deleteAccountRequestsColumns,
} from "./delete-account-data-table";
import { useQuery } from "@tanstack/react-query";

const DeleteAccount = () => {
  const trpc = useTRPC();
  const { data: deleteAccountRequests } = useQuery(trpc.deleteAccounts.getDeleteAccountReq.queryOptions());

  if (!deleteAccountRequests) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Delete Accounts
      </h1>

      <DeleteAccountDataTable
        columns={deleteAccountRequestsColumns}
        data={deleteAccountRequests}
      />
    </>
  );
};

export default DeleteAccount;
