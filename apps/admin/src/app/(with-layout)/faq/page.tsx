'use client';

import { useTR<PERSON> } from "~/trpc/react";
import { faqDataTableColumns, FaqDataTable } from "./faq-data-table";
import FaqFormSheet from "./faq-form-sheet";
import { FaqPagesEnum, ProjectEnum } from "@repo/database";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{
//   faq_id?: string;
//   page?: string;
//   project?: string;
// }>;

const FaqPage = () => {
  const trpc = useTRPC();
  // const searchParams = await props.searchParams;

  // const project = (searchParams.project ??
  //   ProjectEnum.B2B_DEER_CONNECT) as ProjectEnum;
  // const page = (searchParams.page ??
  //   FaqPagesEnum.HELP_CENTER_PAGE) as FaqPagesEnum;

  const project = ProjectEnum.B2B_DEER_CONNECT;
  const page = FaqPagesEnum.HELP_CENTER_PAGE;

  const { data: faqs} = useQuery(trpc.faq.getAllFaq.queryOptions({
    page: page,
    project: project,
  }));

  // if (searchParams.faq_id) {
  //   await api.faq.getFaqById.prefetch({ id: searchParams.faq_id });
  // }
  if (!faqs) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">FAQs</h1>

      <FaqFormSheet project={project} page={page} />
      <FaqDataTable data={faqs} columns={faqDataTableColumns} />
    </>
  );
};

export default FaqPage;
