'use client'
import { useTRPC } from "~/trpc/react";
import { resumeDataTableColumns, ResumeDataTable } from "./resume-data-table";
import { useQuery } from "@tanstack/react-query";

const ResumesPage = () => {
  const trpc = useTRPC();
  const { data: applications} =  useQuery(trpc.career.getAllApplications.queryOptions());

  if (!applications) {
    return null;
  }

  return (
    <>
      <h1 className="font-airbnb_w_md text-3xl font-semibold">Resumes</h1>

      <ResumeDataTable data={applications} columns={resumeDataTableColumns} />
    </>
  );
};

export default ResumesPage;
