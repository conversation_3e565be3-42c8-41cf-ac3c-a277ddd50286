'use client';
import { useTRPC } from "~/trpc/react";
import {
  amenitiesDataTableColumns,
  AmenitiesDataTable,
} from "./amenities-data-table";
import AmenitiesFormSheet from "./amenities-form-sheet";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ amenity_id?: string }>;

const AmenitiesPage = () => {
  const trpc = useTRPC();
  // const searchParams = useSearchParams();
  const { data: amenities } = useQuery(trpc.amenities.getAllAmenities.queryOptions());

  // if (searchParams.amenity_id) {
  //   useQuery(trpc.amenities.getAmenityById.queryOptions({
  //     id: searchParams.amenity_id,
  //   }));
  // }

  if (!amenities) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Amenities
      </h1>

      <AmenitiesFormSheet />
      <AmenitiesDataTable
        data={amenities}
        columns={amenitiesDataTableColumns}
      />
    </>
  );
};

export default AmenitiesPage;
