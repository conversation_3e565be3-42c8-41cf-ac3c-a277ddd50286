"use client";

import { useSearchParams } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@repo/ui/components/ui/dialog";
import AgentDetail from "@repo/ui/components/shared/agent-detail";
import { useTRPC } from "~/trpc/react";
import AgentTestimonialsSwiper from "./agents-testimonials-swiper";
import AgentPostCardsSwiper from "./agent-post-cards-swiper";
import AgentPropertyCardsSwiper from "./agent-property-cards-swiper";
import { useQuery } from "@tanstack/react-query";

const AgentDetailDialogprovider = () => {
  const searchParams = useSearchParams();
  const trpc = useTRPC();
  const agentId = searchParams.get("viewAgentId") ?? "";
  const { data: agent } = useQuery(trpc.partner.getPartnerDetails.queryOptions({
    agentId: agentId,
  }));

  if (!agent) {
    return;
  }

  const handleClose = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete("viewAgentId");
    history.pushState(null, "", `?${params.toString()}`);
  };

  return (
    <>
      <Dialog
        open={agentId ? true : false}
        onOpenChange={(open) => {
          if (!open) handleClose();
        }}
      >
        <DialogContent
          onEscapeKeyDown={() => {
            handleClose();
          }}
        >
          <DialogTitle className="hidden">Agent Details</DialogTitle>
          <AgentDetail
            agent={agent}
            agentPostsCardSwiper={<AgentPostCardsSwiper posts={agent.posts} />}
            agentPropertyCardsSwiper={
              <AgentPropertyCardsSwiper properties={agent.properties} />
            }
            videoReviews={<AgentTestimonialsSwiper agentId={agentId} />}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AgentDetailDialogprovider;
