'use client';

import React from "react";
import { useTRPC } from "~/trpc/react";
import { CustomerDataTable } from "./customers-data-table";
import { useQuery } from "@tanstack/react-query";

const CustomersPage = () => {
  const trpc = useTRPC();
  const { data: customer } =  useQuery(trpc.customer.getCustomers.queryOptions());

  // const customers = await api.customer.getCustomers();
  
  if(!customer) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Customers
      </h1>

      <CustomerDataTable data={customer} />
    </>
  );
};

export default CustomersPage;
