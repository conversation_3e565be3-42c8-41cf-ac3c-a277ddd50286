"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { skipToken, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { useTRPC } from "~/trpc/react";
import { PostPropertyFormFieldStatusEnum } from "@repo/database";
import addUpdateCategoriesSchema from "~/server/api/validations/add-update-categories.validation";
import { useSheet } from "~/app/hooks/use-sheet";
import { CategoryParamName } from "~/app/helpers/constants";

type FormSchemaType = z.infer<typeof addUpdateCategoriesSchema>;

const CategoriesAddUpdateForm = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { paramValue: categoryId, closeSheet } = useSheet(CategoryParamName);

  const {
    data: categoryDetail,
    isLoading,
    error,
  } = useQuery(trpc.propertyCategories.getCategoryById.queryOptions(
    categoryId ? { id: categoryId } : skipToken,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  ));

  const { mutate: updateCategory, isPending: isPendingUpdateMutation } =
    useMutation(trpc.propertyCategories.updateCategory.mutationOptions());
  const { mutate: addCategory, isPending: isPendingAddMutation } =
    useMutation(trpc.propertyCategories.addCategory.mutationOptions());

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(addUpdateCategoriesSchema),
    defaultValues: categoryDetail
      ? {
          name: categoryDetail.name,
          showBedrooms: categoryDetail.showBedrooms,
          showBathrooms: categoryDetail.showBathrooms,
          showSecurityDeposit: categoryDetail.showSecurityDeposit,
          showAreaIn: categoryDetail.showAreaIn,
          showArea: categoryDetail.showArea,
          showAboutProperty: categoryDetail.showAboutProperty,
          showPropertyAddress: categoryDetail.showPropertyAddress,
          showPropertyLocation: categoryDetail.showPropertyLocation,
          showUtilities: categoryDetail.showUtilities,
          showSocietyName: categoryDetail.showSocietyName,
          showBuildYear: categoryDetail.showBuildYear,
          showPossessionState: categoryDetail.showPossessionState,
          showAmenities: categoryDetail.showAmenities,
          showFurnishing: categoryDetail.showFurnishing,
          showFacing: categoryDetail.showFacing,
          showTotalFloors: categoryDetail.showTotalFloors,
          showFloorNumber: categoryDetail.showFloorNumber,
          showCarParking: categoryDetail.showCarParking,
          showPropertyState: categoryDetail.showPropertyState,
        }
      : {
          name: "",
          showBedrooms: PostPropertyFormFieldStatusEnum.HIDE,
          showBathrooms: PostPropertyFormFieldStatusEnum.HIDE,
          showSecurityDeposit: PostPropertyFormFieldStatusEnum.HIDE,
          showAreaIn: PostPropertyFormFieldStatusEnum.HIDE,
          showArea: PostPropertyFormFieldStatusEnum.HIDE,
          showAboutProperty: PostPropertyFormFieldStatusEnum.HIDE,
          showPropertyAddress: PostPropertyFormFieldStatusEnum.HIDE,
          showPropertyLocation: PostPropertyFormFieldStatusEnum.HIDE,
          showUtilities: PostPropertyFormFieldStatusEnum.HIDE,
          showSocietyName: PostPropertyFormFieldStatusEnum.HIDE,
          showBuildYear: PostPropertyFormFieldStatusEnum.HIDE,
          showPossessionState: PostPropertyFormFieldStatusEnum.HIDE,
          showAmenities: PostPropertyFormFieldStatusEnum.HIDE,
          showFurnishing: PostPropertyFormFieldStatusEnum.HIDE,
          showFacing: PostPropertyFormFieldStatusEnum.HIDE,
          showTotalFloors: PostPropertyFormFieldStatusEnum.HIDE,
          showFloorNumber: PostPropertyFormFieldStatusEnum.HIDE,
          showCarParking: PostPropertyFormFieldStatusEnum.HIDE,
          showPropertyState: PostPropertyFormFieldStatusEnum.HIDE,
        },
  });

  const onSubmit = (values: FormSchemaType) => {
    if (categoryId) {
      updateCategory(
        { ...values, id: categoryId },
        {
          onSuccess: (response) => {
            toast.success(response.message);
            void queryClient.invalidateQueries({
              queryKey: [
                trpc.propertyCategories.getCategoryById.queryKey({
                  id: categoryId,
                }),
                trpc.propertyCategories.getAllCategories.queryKey(),
              ],
            });
            closeSheet();
          },
          onError: (error) => {
            toast.error(error.message);
          },
        },
      );
    } else {
      addCategory(values, {
        onSuccess: (response) => {
          toast.success(response.message);
          void queryClient.invalidateQueries({
            queryKey: trpc.propertyCategories.getAllCategories.queryKey(),
          });
          closeSheet();
        },
        onError: (error) => {
          toast.error(error.message);
        },
      });
    }
  };

  if (isLoading && categoryId) {
    return <div className="p-4 text-center">Loading category details...</div>;
  }

  if (error && categoryId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading category: {error.message}
      </div>
    );
  }

  // Field configurations
  const STEP_1_FORM_FIELDS = [
    { name: "showBedrooms", label: "Bedrooms" },
    { name: "showBathrooms", label: "Bathrooms" },
    { name: "showSecurityDeposit", label: "Security Deposit" },
    { name: "showAreaIn", label: "Area Unit" },
    { name: "showArea", label: "Area" },
    { name: "showAboutProperty", label: "About Property" },
  ];

  const STEP_2_FORM_FIELDS = [
    { name: "showPropertyAddress", label: "Property Address" },
    {
      name: "showPropertyLocation",
      label: "Flat, House no., Building, Company, Apartment",
    },
    { name: "showUtilities", label: "Utilities" },
  ];

  const STEP_3_FORM_FIELDS = [
    { name: "showSocietyName", label: "Society Name" },
    { name: "showBuildYear", label: "Build Year" },
    { name: "showPossessionState", label: "Possession State" },
    { name: "showAmenities", label: "Amenities" },
    { name: "showFurnishing", label: "Furnishing" },
    { name: "showFacing", label: "Facing" },
    { name: "showTotalFloors", label: "Total Floors" },
    { name: "showFloorNumber", label: "Floor Number" },
    { name: "showCarParking", label: "Car Parking" },
    { name: "showPropertyState", label: "Property State" },
  ];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium">Category Name</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter category name"
                  {...field}
                  className="w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-8">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Step 1 fields</h3>
            <div className="grid gap-4 md:grid-cols-2">
              {STEP_1_FORM_FIELDS.map(({ name, label }) => (
                <FormField
                  key={name}
                  control={form.control}
                  name={name as keyof FormSchemaType}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium">{label}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue>
                              {field.value ===
                                PostPropertyFormFieldStatusEnum.SHOW && "Show"}
                              {field.value ===
                                PostPropertyFormFieldStatusEnum.HIDE && "Hide"}
                              {field.value ===
                                PostPropertyFormFieldStatusEnum.OPTIONAL &&
                                "Optional"}
                            </SelectValue>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem
                            value={PostPropertyFormFieldStatusEnum.SHOW}
                          >
                            Show
                          </SelectItem>
                          <SelectItem
                            value={PostPropertyFormFieldStatusEnum.HIDE}
                          >
                            Hide
                          </SelectItem>
                          <SelectItem
                            value={PostPropertyFormFieldStatusEnum.OPTIONAL}
                          >
                            Optional
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Step 2 fields</h3>
            <div className="grid gap-4 md:grid-cols-2">
              {STEP_2_FORM_FIELDS.map(({ name, label }) => (
                <FormField
                  key={name}
                  control={form.control}
                  name={name as keyof FormSchemaType}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium">{label}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue>
                              {field.value ===
                                PostPropertyFormFieldStatusEnum.SHOW && "Show"}
                              {field.value ===
                                PostPropertyFormFieldStatusEnum.HIDE && "Hide"}
                              {field.value ===
                                PostPropertyFormFieldStatusEnum.OPTIONAL &&
                                "Optional"}
                            </SelectValue>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem
                            value={PostPropertyFormFieldStatusEnum.SHOW}
                          >
                            Show
                          </SelectItem>
                          <SelectItem
                            value={PostPropertyFormFieldStatusEnum.HIDE}
                          >
                            Hide
                          </SelectItem>
                          <SelectItem
                            value={PostPropertyFormFieldStatusEnum.OPTIONAL}
                          >
                            Optional
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Step 3 fields</h3>
            <div className="grid gap-4 md:grid-cols-2">
              {STEP_3_FORM_FIELDS.map(({ name, label }) => (
                <FormField
                  key={name}
                  control={form.control}
                  name={name as keyof FormSchemaType}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium">{label}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue>
                              {field.value ===
                                PostPropertyFormFieldStatusEnum.SHOW && "Show"}
                              {field.value ===
                                PostPropertyFormFieldStatusEnum.HIDE && "Hide"}
                              {field.value ===
                                PostPropertyFormFieldStatusEnum.OPTIONAL &&
                                "Optional"}
                            </SelectValue>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem
                            value={PostPropertyFormFieldStatusEnum.SHOW}
                          >
                            Show
                          </SelectItem>
                          <SelectItem
                            value={PostPropertyFormFieldStatusEnum.HIDE}
                          >
                            Hide
                          </SelectItem>
                          <SelectItem
                            value={PostPropertyFormFieldStatusEnum.OPTIONAL}
                          >
                            Optional
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
            </div>
          </div>
        </div>

        {categoryId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          categoryId && (
            <Button type="submit" className="w-full py-3">
              Update Category
            </Button>
          )
        )}

        {!categoryId && isPendingAddMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !categoryId && (
            <Button type="submit" className="w-full py-3">
              Create Category
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default CategoriesAddUpdateForm;
