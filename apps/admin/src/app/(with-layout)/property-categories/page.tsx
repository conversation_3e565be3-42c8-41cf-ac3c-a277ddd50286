'use client';

import { useTRPC } from "~/trpc/react";
import {
  CategoriesDataTable,
  CategoriesDataTableColumns,
} from "./categories-data-table";
import PropertyCategoriesFormSheet from "./property-categories-form-sheet";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ category_id?: string }>;

const PropertyCategoriesPage = () => {
  const trpc = useTRPC();
  const { data: categories } = useQuery(trpc.propertyCategories.getAllCategories.queryOptions());

  if (!categories) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Property Categories
      </h1>

      <PropertyCategoriesFormSheet />

      <CategoriesDataTable
        data={categories}
        columns={CategoriesDataTableColumns}
      />
    </>
  );
};

export default PropertyCategoriesPage;
