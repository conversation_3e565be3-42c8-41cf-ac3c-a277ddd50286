'use client';

import { useTRPC } from "~/trpc/react";
import { PartnerDataTable } from "./partner-data-table";
import PartnerFormSheet from "./partner-form-sheet";
import PostPropertyFormSheet from "./post-property/post-property-form-sheet";
import CardViewPartners from "./card-view-partners";
import PaginationButtons from "./pagination-buttons";
import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{
//   partner_id?: string;
//   status?: string;
//   view?: "cards" | "table";
//   page?: number;
//   take?: number;
// }>;

const PartnersPage = () => {
  const trpc = useTRPC();
  // const searchParams = await props.searchParams;
  const searchParams = useSearchParams();

  const status = searchParams.get('status') ?? "active";
  const isActive = status === "active";
  const view: "cards" | "table" = searchParams.get('view') === "table" ? "table" : "cards";
  const currentPage = Number(searchParams.get('page') ?? 1);

  const { data: partners } = useQuery(trpc.partner.getPartners.queryOptions({
    active: isActive ? "active" : "inactive",
    page: view === "cards" ? currentPage : undefined,
    take: view === "cards" ? 9 : undefined,
  }));

  // if (searchParams.partner_id) {
  //   await api.partner.getPartnerById.prefetch({
  //     id: searchParams.partner_id,
  //   });
  // }

  // await api.partner.getCities.prefetch();

  if (!partners) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        {isActive ? "Active" : "Inactive"} Partners
      </h1>

      <div className="flex items-center justify-between gap-x-4">
        <PartnerFormSheet
          activeStatus={isActive ? "active" : "inactive"}
          view={view}
        />
        {view === "cards" && (
          <PaginationButtons
            currentPage={currentPage}
            totalPages={partners.totalPages}
            status={status}
            view={view}
          />
        )}
      </div>

      {view === "cards" ? (
        <CardViewPartners partners={partners.partners} />
      ) : (
        <PartnerDataTable data={partners.partners} />
      )}

      <PostPropertyFormSheet />
    </>
  );
};

export default PartnersPage;
