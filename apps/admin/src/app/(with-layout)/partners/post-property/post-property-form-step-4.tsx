/**
 * PostPropertyFormStep4 Component
 *
 * The fourth step of the property posting form that handles property media uploads.
 * This component manages:
 * 1. Multiple media sections for organizing property images
 * 2. Image upload functionality with Cloudinary integration
 * 3. Image validation and preview
 * 4. Section management (add/remove sections)
 *
 * Features:
 * - Multiple image upload support
 * - Cloudinary integration
 * - Image type validation (PNG/JPEG)
 * - Dynamic section management
 * - Minimum 3 images per section requirement
 * - Responsive layout with proper error handling
 *
 * @component
 */

import { useForm } from "@tanstack/react-form";
import { Dialog, DialogContent } from "@repo/ui/components/ui/default-dialog";
import { Input } from "@repo/ui/components/ui/input";
import { Button } from "@repo/ui/components/ui/button";
import { Plus, TrashIcon } from "lucide-react";
import { toast } from "@repo/ui/components/ui/sonner";
import { useState, useEffect } from "react";
import usePostPropertyForm from "./use-post-property-form";
import PostPropertyFooter from "./post-property-footer";
import FormLabel from "~/app/components/shared/form-label";
import FieldError from "~/app/components/shared/field-error";
import Image from "next/image";
import { useTRPC } from "~/trpc/react";
import type { PostPropertyStep4Base } from "~/server/api/validations/post-property.validation";
import { skipToken, useMutation, useQuery } from "@tanstack/react-query";
import FormLoading from "./form-loading";
import { CldUploadWidget, CldVideoPlayer } from "next-cloudinary";
import type { CloudinaryUploadWidgetResults } from "next-cloudinary";
import { env } from "~/env";
import { PropertyMediaTypeEnum } from "@repo/database";

const PostPropertyFormStep4 = () => {
  const trpc = useTRPC();
  const [isSettingValues, setIsSettingValues] = useState(true);
  const [isAddingSectionOpen, setIsAddingSectionOpen] = useState(false);
  const { agentId, propertyId, handleCloseForm } = usePostPropertyForm();

  const { data: propertyStep4Data, isPending } = useQuery(trpc.property.getStep4.queryOptions(
    propertyId ? { propertyId } : skipToken,
    {
      refetchOnMount: true,
      refetchOnWindowFocus: true,
      staleTime: 0,
    },
  ));

  const { mutate: submitStep4 } = useMutation(trpc.property.submitStep4.mutationOptions());
  const { mutate: deletePropertyMedia } =
    useMutation(trpc.property.deletePropertyMedia.mutationOptions());

  const form = useForm({
    defaultValues: {
      mediaSections: [
        {
          title: "General",
          media: [],
        },
      ],
    } as PostPropertyStep4Base,
    onSubmit: ({ value }) => {
      if (!agentId || !propertyId) {
        return toast.error("One or both IDs missing (Agent/Property)");
      }

      submitStep4(
        { ...value, propertyId },
        {
          onSuccess: (data) => {
            toast.success(data.message);
            void handleCloseForm();
          },
          onError: (error) => {
            toast.error(error.message);
          },
        },
      );
    },
  });

  const handleCloudinaryUpload = (
    result: CloudinaryUploadWidgetResults,
    sectionIndex: number,
  ) => {
    const { info } = result as {
      info: { public_id: string; secure_url: string; format: string };
      event: string;
    };

    const cloudinaryId = info.public_id;
    const cloudinaryUrl = info.secure_url;
    const mediaType = ["png", "jpg", "jpeg"].includes(info.format)
      ? PropertyMediaTypeEnum.IMAGE
      : PropertyMediaTypeEnum.VIDEO;

    if (!cloudinaryId || !cloudinaryUrl) {
      toast.error("Upload failed");
      return;
    }

    try {
      const mediaSections = [...form.getFieldValue("mediaSections")];

      if (sectionIndex >= 0 && sectionIndex < mediaSections.length) {
        // @ts-expect-error: TypeScript doesn't know that mediaSections[sectionIndex] is defined
        mediaSections[sectionIndex] = {
          ...mediaSections[sectionIndex],
          media: [
            ...(mediaSections[sectionIndex]?.media ?? []),
            {
              cloudinaryId: cloudinaryId,
              cloudinaryUrl: cloudinaryUrl,
              mediaType: mediaType,
            },
          ],
        };

        form.setFieldValue("mediaSections", mediaSections);
        void form.validateField("mediaSections", "change");
        toast.success(`Successfully uploaded file`);
      }
    } catch (error) {
      console.error("Error in upload process:", error);
      toast.error("Failed to upload file. Please try again.");
    }
  };

  const handleVideoDelete = (cloudinaryId?: string) => {
    console.log("Deleting video with ID:", cloudinaryId);

    deletePropertyMedia({
      cloudinaryId: cloudinaryId,
    });
  };

  useEffect(() => {
    if (propertyStep4Data) {
      setIsSettingValues(true);

      const formattedSections = propertyStep4Data.map((section) => ({
        title: section.title,
        media: section.media.map((media) => ({
          fileKey: media.fileKey ?? undefined,
          filePublicUrl: media.filePublicUrl ?? undefined,
          cloudinaryUrl: media.cloudinaryUrl ?? undefined,
          cloudinaryId: media.cloudinaryId ?? undefined,
          mediaType: media.mediaType ?? undefined,
        })),
      }));

      if (formattedSections.length === 0) {
        form.setFieldValue("mediaSections", [
          {
            title: "General",
            media: [],
          },
        ]);
      } else {
        form.setFieldValue("mediaSections", formattedSections);
      }

      void form.validateAllFields("mount");
      setIsSettingValues(false);
    }
  }, [propertyStep4Data, form]);

  if ((isPending || isSettingValues) && propertyId) {
    return <FormLoading />;
  }

  return (
    <>
      <div className="container mx-auto max-w-full space-y-6">
        <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:space-y-1.5 xl:space-y-2 2xl:space-y-2.5">
          <div>
            <h2 className="font-airbnb_w_xbd text-xl font-extrabold text-primary-2-700 lg:text-2xl 2xl:text-3xl">
              Media
            </h2>
            <p className="font-airbnb_w_bk text-[10px] text-text-550 sm:text-xs md:text-sm lg:text-base 2xl:text-lg">
              Add minimum 3 photos in a section to continue further
            </p>
          </div>

          <div
            className="inline-flex h-8 cursor-pointer items-center justify-center gap-1 self-end"
            onClick={() => setIsAddingSectionOpen(true)}
          >
            <Plus className="text-primary-2-700" />
            <div className="font-airbnb_w_md text-lg font-medium leading-7 text-primary-2-700">
              Add Media Section
            </div>
          </div>
        </div>

        <form.Field
          name="mediaSections"
          validators={{
            onChange: (mediaSectionsField) => {
              const errors: string[] = [];
              const mediaSections = mediaSectionsField.value;

              if (Array.isArray(mediaSections)) {
                mediaSections.forEach((section, index) => {
                  if (!section.title) {
                    errors.push(`Section ${index + 1}: Title is required`);
                  }
                  if (section.media.length < 3) {
                    errors.push(
                      `Section ${index + 1}: Minimum 3 photos required`,
                    );
                  }
                });
              }

              return errors.length ? errors : undefined;
            },
          }}
        >
          {(field) => (
            <div className="space-y-6">
              {field.state.value.map((section, sectionIndex) => (
                <div key={sectionIndex} className="rounded-lg border p-4">
                  <div className="mb-2">
                    <FormLabel>{section.title}</FormLabel>
                  </div>

                  <div className="mb-4 grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
                    {section.media.map((media, mediaIndex) => (
                      <div
                        key={mediaIndex}
                        className="relative aspect-video overflow-hidden rounded-sm md:rounded-lg"
                      >
                        {media.mediaType === PropertyMediaTypeEnum.VIDEO ? (
                          media.cloudinaryUrl ? (
                            <CldVideoPlayer
                              src={media.cloudinaryUrl}
                              className="h-full w-full object-contain"
                              controls
                            />
                          ) : null
                        ) : (
                          <Image
                            src={
                              media.cloudinaryUrl ??
                              media.filePublicUrl ??
                              "/images/placeholder-user-image.jpg"
                            }
                            alt={`Media ${mediaIndex + 1}`}
                            height={200}
                            width={200}
                            className="h-full w-full object-cover"
                            unoptimized
                          />
                        )}
                        <Button
                          variant="outline"
                          type="button"
                          className="absolute right-2 top-2"
                          onClick={() => {
                            const newSections = [...field.state.value];
                            if (newSections[sectionIndex]) {
                              newSections[sectionIndex] = {
                                ...newSections[sectionIndex],
                                media: [
                                  ...newSections[sectionIndex].media.slice(
                                    0,
                                    mediaIndex,
                                  ),
                                  ...newSections[sectionIndex].media.slice(
                                    mediaIndex + 1,
                                  ),
                                ],
                              };
                              field.handleChange(newSections);
                              handleVideoDelete(media.cloudinaryId);
                            }
                          }}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>

                  <div className="flex flex-col space-y-4">
                    {/* Cloudinary Upload Widget */}
                    <CldUploadWidget
                      signatureEndpoint="/api/cloudinary"
                      uploadPreset={env.NEXT_PUBLIC_CLOUDINARY_CLOUD_PRESET}
                      onSuccess={(result) =>
                        handleCloudinaryUpload(result, sectionIndex)
                      }
                      options={{
                        croppingShowDimensions: true,
                        croppingValidateDimensions: true,
                        showSkipCropButton: false,
                        maxFiles: 5,
                        showPoweredBy: false,
                        resourceType: "auto",
                        clientAllowedFormats: [
                          "png",
                          "jpg",
                          "jpeg",
                          "mp4",
                          "mov",
                          "avi",
                          "mkv",
                        ],
                        folder: `${env.NEXT_PUBLIC_CLOUDINARY_BASE_FOLDER_NAME}/users/${agentId}/properties/${propertyId}`,
                        styles: { zIndex: 1000, position: "fixed" },
                      }}
                    >
                      {({ open }) => (
                        <Button
                          type="button"
                          onClick={() => open()}
                          className="w-full"
                        >
                          Upload Media (Images/Videos)
                        </Button>
                      )}
                    </CldUploadWidget>
                  </div>

                  {field.state.value.length > 1 && (
                    <Button
                      variant="destructive"
                      onClick={() => {
                        const newSections = [...field.state.value];
                        newSections.splice(sectionIndex, 1);
                        field.handleChange(newSections);
                      }}
                      className="mt-4"
                    >
                      Remove Section
                    </Button>
                  )}
                </div>
              ))}

              <FieldError>{field.state.meta.errors}</FieldError>
            </div>
          )}
        </form.Field>
      </div>

      <Dialog open={isAddingSectionOpen} onOpenChange={setIsAddingSectionOpen}>
        <DialogContent>
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Add New Media Section</h3>
            <Input
              placeholder="Section Title"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  const title = e.currentTarget.value;
                  if (title) {
                    const mediaSections = [
                      ...form.getFieldValue("mediaSections"),
                    ];
                    form.setFieldValue("mediaSections", [
                      ...mediaSections,
                      { title, media: [] },
                    ]);
                    setIsAddingSectionOpen(false);
                  }
                }
              }}
            />
          </div>
        </DialogContent>
      </Dialog>

      <form.Subscribe
        selector={(state) => [state.canSubmit, state.isSubmitting]}
        children={([canSubmit, isSubmitting]) => (
          <PostPropertyFooter
            canSubmit={canSubmit}
            isSubmitting={isSubmitting}
            handleNextStep={async () => await form.handleSubmit()}
          />
        )}
      />
    </>
  );
};

export default PostPropertyFormStep4;
