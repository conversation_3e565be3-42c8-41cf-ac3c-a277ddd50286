/**
 * PostPropertyFormStep3 Component
 *
 * The third step of the property posting form that collects property attributes and amenities.
 * This component handles:
 * 1. Property attributes like society/locality name
 * 2. Property amenities and features
 * 3. Additional property specifications
 * 4. Property condition and maintenance details
 *
 * Features:
 * - Dynamic form fields based on property type
 * - Validation for required attributes
 * - Organized grouping of related attributes
 * - Responsive layout with proper error handling
 *
 * @component
 */

import usePostPropertyForm from "./use-post-property-form";
import PostPropertyFooter from "./post-property-footer";
import { useForm } from "@tanstack/react-form";
import { PostPropertyStep3BaseSchema } from "~/server/api/validations/post-property.validation";
import type { PostPropertyStep3Base } from "~/server/api/validations/post-property.validation";
import { useTRPC } from "~/trpc/react";
import FormLabel from "~/app/components/shared/form-label";
import { Input } from "@repo/ui/components/ui/input";
import FieldError from "~/app/components/shared/field-error";
import {
  FacingEnum,
  FurnishingEnum,
  PossessionStateEnum,
  PropertyStateEnum,
} from "@repo/database";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { MultipleSelector } from "@repo/ui/components/ui/multiple-selector";
import { toast } from "@repo/ui/components/ui/sonner";
import { skipToken, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import FormLoading from "./form-loading";
import { useEffect, useState } from "react";

const PostPropertyFormStep3 = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const [isSettingValues, setIsSettingValues] = useState(true);
  const { goToNextStep, categoryId, agentId, propertyId } =
    usePostPropertyForm();

  const { data: selectedCategory } =
    useQuery(trpc.propertyCategories.getCategoryById.queryOptions(
      categoryId ? { id: categoryId } : skipToken,
    ));

  const { data: propertyStep3Data, isPending } = useQuery(trpc.property.getStep3.queryOptions(
    propertyId && agentId ? { propertyId: propertyId } : skipToken,
    { refetchOnMount: true, refetchOnWindowFocus: true, staleTime: 0 },
  ));

  const { data: amenities } = useQuery(trpc.amenities.getAllAmenities.queryOptions());
  const { mutate: submitStep3 } = useMutation(trpc.property.submitStep3.mutationOptions());

  const form = useForm({
    defaultValues: {
      societyOrLocalityName: undefined,
      buildYear: undefined,
      possessionState: undefined,
      amenities: undefined,
      furnishing: undefined,
      totalFloors: undefined,
      floorNumber: undefined,
      carParking: undefined,
      facing: undefined,
      propertyState: undefined,
    } as PostPropertyStep3Base,
    onSubmit: ({ value }) => {
      if (!agentId || !propertyId) {
        return toast.error("One or both IDs missing (Agent/Property)");
      }

      submitStep3(
        { ...value, agentId: agentId, propertyId: propertyId },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            void queryClient.invalidateQueries({
              queryKey: trpc.property.getStep3.queryKey(),
            });
            void goToNextStep();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    },
  });

  useEffect(() => {
    if (propertyStep3Data) {
      setIsSettingValues(true);
      form.setFieldValue(
        "societyOrLocalityName",
        propertyStep3Data.societyOrLocalityName ?? undefined,
      );
      form.setFieldValue("buildYear", propertyStep3Data.buildYear ?? undefined);
      form.setFieldValue(
        "possessionState",
        propertyStep3Data.possessionState ?? undefined,
      );

      if (propertyStep3Data.amenities.length > 0) {
        form.setFieldValue(
          "amenities",
          propertyStep3Data.amenities.map((amenity) => ({
            id: amenity.id,
            name: amenity.name,
          })),
        );
      } else {
        form.setFieldValue("amenities", undefined);
      }

      form.setFieldValue(
        "furnishing",
        propertyStep3Data.furnishing ?? undefined,
      );
      form.setFieldValue(
        "totalFloors",
        propertyStep3Data.totalFloors ?? undefined,
      );
      form.setFieldValue(
        "floorNumber",
        propertyStep3Data.floorNumber ?? undefined,
      );
      form.setFieldValue(
        "carParking",
        propertyStep3Data.carParking ?? undefined,
      );
      form.setFieldValue("facing", propertyStep3Data.facing ?? undefined);
      form.setFieldValue(
        "propertyState",
        propertyStep3Data.propertyState ?? undefined,
      );

      // Validate all fields after setting values
      void form.validateAllFields("mount");
      setIsSettingValues(false);
    }
  }, [propertyStep3Data, form]);

  if ((isPending || isSettingValues) && propertyId) {
    return <FormLoading />;
  }

  return (
    <>
      <div className="container mx-auto mb-10 max-w-full">
        <div className="flex justify-between lg:space-y-1.5 xl:space-y-2 2xl:space-y-2.5">
          <div className="space-y-1 lg:space-y-1.5 xl:space-y-2 2xl:space-y-2.5">
            <h2 className="font-airbnb_w_xbd text-xl font-extrabold text-primary-2-700 lg:text-2xl 2xl:text-3xl">
              Attributes
            </h2>
            <p className="font-airbnb_w_bk text-[10px] text-text-550 sm:text-xs md:text-sm lg:text-base 2xl:text-lg">
              Provide attributes of your property
            </p>
          </div>
        </div>
      </div>

      <form className="container mx-auto max-w-full space-y-4">
        <div className="flex items-center gap-4">
          {/* Society/Locality Name */}
          {selectedCategory?.showSocietyName !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="societyOrLocalityName"
                validators={{
                  onChange:
                    selectedCategory?.showSocietyName === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          societyOrLocalityName: true,
                        }).shape.societyOrLocalityName.refine((val) =>
                          !val ? false : true,
                        )
                      : selectedCategory?.showSocietyName === "OPTIONAL"
                        ? PostPropertyStep3BaseSchema.pick({
                            societyOrLocalityName: true,
                          }).shape.societyOrLocalityName
                        : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel htmlFor={field.name}>
                      Society / Locality Name
                      {selectedCategory?.showSocietyName === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <Input
                      id={field.name}
                      name={field.name}
                      value={field.state.value ?? ""}
                      onChange={(e) => {
                        const value =
                          e.target.value === "" ? undefined : e.target.value;
                        field.handleChange(value);
                      }}
                    />
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}

          {/* Build Year */}
          {selectedCategory?.showBuildYear !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="buildYear"
                validators={{
                  onChange:
                    selectedCategory?.showBuildYear === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          buildYear: true,
                        }).shape.buildYear.refine((val) =>
                          !val ? false : true,
                        )
                      : selectedCategory?.showBuildYear === "OPTIONAL"
                        ? PostPropertyStep3BaseSchema.pick({
                            buildYear: true,
                          }).shape.buildYear
                        : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel htmlFor={field.name}>
                      Build Year
                      {selectedCategory?.showBuildYear === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <Input
                      id={field.name}
                      name={field.name}
                      type="number"
                      value={field.state.value ?? ""}
                      onChange={(e) => {
                        const value =
                          e.target.value === ""
                            ? undefined
                            : parseInt(e.target.value);
                        field.handleChange(value);
                      }}
                    />
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}
        </div>

        <div className="flex items-center gap-4">
          {/* Possession State */}
          {selectedCategory?.showPossessionState !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="possessionState"
                validators={{
                  onChange:
                    selectedCategory?.showPossessionState === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          possessionState: true,
                        }).shape.possessionState.refine((val) =>
                          !val ? false : true,
                        )
                      : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel htmlFor={field.name}>
                      Possession State
                      {selectedCategory?.showPossessionState === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <Select
                      value={field.state.value}
                      onValueChange={(value) =>
                        field.handleChange(value as PossessionStateEnum)
                      }
                    >
                      <SelectTrigger id={field.name}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(PossessionStateEnum).map((state) => (
                          <SelectItem key={state} value={state}>
                            {state.replaceAll("_", " ").toLowerCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}

          {/* Amenities */}
          {selectedCategory?.showAmenities !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="amenities"
                validators={{
                  onChange:
                    selectedCategory?.showAmenities === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          amenities: true,
                        }).shape.amenities.refine((val) =>
                          !val || val.length <= 0 ? false : true,
                        )
                      : selectedCategory?.showAmenities === "OPTIONAL"
                        ? PostPropertyStep3BaseSchema.pick({ amenities: true })
                            .shape.amenities
                        : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel
                      className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                      htmlFor={field.name}
                    >
                      Amenities
                      {selectedCategory?.showAmenities === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <MultipleSelector
                      value={(field.state.value ?? []).map((amenity) => ({
                        value: amenity.id,
                        label: amenity.name,
                      }))}
                      onChange={(newValue) => {
                        const selectedAmenities = newValue.map((v) => ({
                          id: v.value,
                          name:
                            amenities?.find((a) => a.id === v.value)?.name ??
                            "",
                        }));
                        field.handleChange(selectedAmenities);
                      }}
                      options={amenities?.map((amenity) => ({
                        value: amenity.id,
                        label: amenity.name,
                      }))}
                      placeholder="Select amenities..."
                    />
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}
        </div>

        <div className="flex items-center gap-4">
          {/* Facing */}
          {selectedCategory?.showFacing !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="facing"
                validators={{
                  onChange:
                    selectedCategory?.showFacing === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          facing: true,
                        }).shape.facing.refine((val) => (!val ? false : true))
                      : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel htmlFor={field.name}>
                      Facing Direction
                      {selectedCategory?.showFacing === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <Select
                      value={field.state.value}
                      onValueChange={(value) =>
                        field.handleChange(value as FacingEnum)
                      }
                    >
                      <SelectTrigger id={field.name}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(FacingEnum).map((direction) => (
                          <SelectItem key={direction} value={direction}>
                            {direction.replace("_", " ")}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}

          {/* Furnishing */}
          {selectedCategory?.showFurnishing !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="furnishing"
                validators={{
                  onChange:
                    selectedCategory?.showFurnishing === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          furnishing: true,
                        }).shape.furnishing.refine((val) =>
                          !val ? false : true,
                        )
                      : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel htmlFor={field.name}>
                      Furnishing Status
                      {selectedCategory?.showFurnishing === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <Select
                      value={field.state.value}
                      onValueChange={(value) =>
                        field.handleChange(value as FurnishingEnum)
                      }
                    >
                      <SelectTrigger id={field.name}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(FurnishingEnum).map((state) => (
                          <SelectItem key={state} value={state}>
                            {state}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}
        </div>

        <div className="flex items-center gap-4">
          {/* Total Floors */}
          {selectedCategory?.showTotalFloors !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="totalFloors"
                validators={{
                  onChange:
                    selectedCategory?.showTotalFloors === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          totalFloors: true,
                        }).shape.totalFloors.refine((val) =>
                          !val ? false : true,
                        )
                      : selectedCategory?.showTotalFloors === "OPTIONAL"
                        ? PostPropertyStep3BaseSchema.pick({
                            totalFloors: true,
                          }).shape.totalFloors
                        : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel htmlFor={field.name}>
                      Total Floors
                      {selectedCategory?.showTotalFloors === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <Input
                      id={field.name}
                      name={field.name}
                      type="number"
                      value={field.state.value ?? ""}
                      onChange={(e) => {
                        const value =
                          e.target.value === ""
                            ? undefined
                            : parseInt(e.target.value);
                        field.handleChange(value);
                      }}
                    />
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}

          {/* floor number */}
          {selectedCategory?.showFloorNumber !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="floorNumber"
                validators={{
                  onChange:
                    selectedCategory?.showFloorNumber === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          floorNumber: true,
                        }).shape.floorNumber.refine((val) =>
                          !val ? false : true,
                        )
                      : selectedCategory?.showFloorNumber === "OPTIONAL"
                        ? PostPropertyStep3BaseSchema.pick({
                            floorNumber: true,
                          }).shape.floorNumber
                        : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel htmlFor={field.name}>
                      Floor Number
                      {selectedCategory?.showFloorNumber === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <Input
                      id={field.name}
                      name={field.name}
                      type="number"
                      value={field.state.value ?? ""}
                      onChange={(e) => {
                        const value =
                          e.target.value === ""
                            ? undefined
                            : parseInt(e.target.value);
                        field.handleChange(value);
                      }}
                    />
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}
        </div>

        <div className="flex items-center gap-4">
          {/* Car Parking */}
          {selectedCategory?.showCarParking !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="carParking"
                validators={{
                  onChange:
                    selectedCategory?.showCarParking === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          carParking: true,
                        }).shape.carParking.refine((val) =>
                          !val ? false : true,
                        )
                      : selectedCategory?.showCarParking === "OPTIONAL"
                        ? PostPropertyStep3BaseSchema.pick({ carParking: true })
                            .shape.carParking
                        : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel htmlFor={field.name}>
                      Car Parking
                      {selectedCategory?.showCarParking === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <Input
                      id={field.name}
                      name={field.name}
                      type="number"
                      value={field.state.value ?? ""}
                      onChange={(e) => {
                        const value =
                          e.target.value === ""
                            ? undefined
                            : parseInt(e.target.value);
                        field.handleChange(value);
                      }}
                    />
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}

          {/* Property State */}
          {selectedCategory?.showPropertyState !== "HIDE" && (
            <div className="flex-1">
              <form.Field
                name="propertyState"
                validators={{
                  onChange:
                    selectedCategory?.showPropertyState === "SHOW"
                      ? PostPropertyStep3BaseSchema.pick({
                          propertyState: true,
                        }).shape.propertyState.refine((val) =>
                          !val ? false : true,
                        )
                      : undefined,
                }}
              >
                {(field) => (
                  <>
                    <FormLabel htmlFor={field.name}>
                      Property State
                      {selectedCategory?.showPropertyState === "OPTIONAL"
                        ? " (Optional)"
                        : ""}
                    </FormLabel>
                    <Select
                      value={field.state.value}
                      onValueChange={(value) =>
                        field.handleChange(value as PropertyStateEnum)
                      }
                    >
                      <SelectTrigger id={field.name}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(PropertyStateEnum).map((state) => (
                          <SelectItem key={state} value={state}>
                            {state.replace("_", " ")}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FieldError>
                      {field.state.meta.errors.length > 0
                        ? field.state.meta.errorMap.onChange?.map(
                            (item) => item.message,
                          )
                        : null}
                    </FieldError>
                  </>
                )}
              </form.Field>
            </div>
          )}
        </div>
      </form>

      <form.Subscribe
        selector={(state) => [state.canSubmit, state.isSubmitting]}
        children={([canSubmit, isSubmitting]) => (
          <PostPropertyFooter
            canSubmit={canSubmit}
            isSubmitting={isSubmitting}
            handleNextStep={async () => await form.handleSubmit()}
          />
        )}
      />
    </>
  );
};

export default PostPropertyFormStep3;
