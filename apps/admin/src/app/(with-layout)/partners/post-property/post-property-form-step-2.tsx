/**
 * PostPropertyFormStep2 Component
 *
 * The second step of the property posting form that handles property location and address details.
 * This component manages:
 * 1. Property location selection using Google Places API
 * 2. Address components and formatting
 * 3. Location coordinates (latitude/longitude)
 * 4. Property boundary drawing with polygon markers
 * 5. Utility selection for the property
 *
 * Features:
 * - Google Places Autocomplete integration for address search
 * - Interactive map with drawing tools for property boundaries
 * - Automatic address component parsing from Google Places API
 * - Geolocation support to use user's current location
 * - Form validation for required location fields using Zod
 * - Property boundary visualization with editable markers
 * - Dialog confirmation when proceeding without markers
 *
 * @component
 * @returns {JSX.Element} Property location and address form step
 */

import usePostPropertyForm from "./use-post-property-form";
import PostPropertyFooter from "./post-property-footer";
import { useForm, useStore } from "@tanstack/react-form";
import { PostPropertyStep2BaseSchema } from "~/server/api/validations/post-property.validation";
import type { PostPropertyStep2Base } from "~/server/api/validations/post-property.validation";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  Di<PERSON>Trigger,
} from "@repo/ui/components/ui/dialog";
import { MapPinnedIcon, PlusIcon, TrashIcon, XIcon } from "lucide-react";
import GoogleAutocompleteInput from "@repo/ui/components/shared/google-autocomplete-input";
import DrawableGoogleMaps from "@repo/ui/components/shared/drawable-google-map";
import { cn } from "@repo/ui/lib/utils";
import { useEffect, useState } from "react";
import { useTRPC } from "~/trpc/react";
import FormLabel from "~/app/components/shared/form-label";
import { Input } from "@repo/ui/components/ui/input";
import FieldError from "~/app/components/shared/field-error";
import { z } from "zod";
import { Button } from "@repo/ui/components/ui/button";
import { toast } from "@repo/ui/components/ui/sonner";
import FormLoading from "./form-loading";
import { skipToken, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

/**
 * Represents a geographic point with latitude and longitude
 * Used for map markers and property boundaries
 *
 * @typedef {Object} Point
 * @property {number} lat - Latitude coordinate
 * @property {number} lng - Longitude coordinate
 */
type Point = {
  lat: number;
  lng: number;
};

/**
 * Represents a location selected from Google Places API
 * Contains address details and geographic coordinates
 *
 * @typedef {Object} SelectedLocation
 * @property {string} lat - Latitude as string
 * @property {string} lng - Longitude as string
 * @property {string} place_id - Google Place ID for the location
 * @property {any} address_components - Address components from Google Places API
 * @property {string} address - Formatted address string
 */
type SelectedLocation = {
  lat: string;
  lng: string;
  place_id: string;
  address_components: any;
  address: string;
};

/**
 * Represents user's current geographic position
 * Used for centering the map on user's location
 *
 * @typedef {Object} UserCurrentPosition
 * @property {number} lat - Latitude coordinate
 * @property {number} lng - Longitude coordinate
 */
type UserCurrentPosition = {
  lat: number;
  lng: number;
};

const PostPropertyFormStep2 = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [disableSearchBar, setDisableSearchBar] = useState<boolean>(false);
  const [searchSelectedLocation, setSearchSelectedLocation] = useState<
    SelectedLocation | undefined
  >(undefined);
  const [drawingPoints, setDrawingPoints] = useState<
    google.maps.LatLngLiteral[]
  >([]);
  const [userLocation, setUserLocation] = useState<
    UserCurrentPosition | undefined
  >(undefined);
  const [showAlert, setShowAlert] = useState<boolean>(false);
  const [locationRestriction, setLocationRestriction] = useState<Point[]>([]);
  const { goToNextStep, agentId, propertyId, categoryId } =
    usePostPropertyForm();
  const [isSettingValues, setIsSettingValues] = useState(true);

  const { data: selectedCategory } =
    useQuery(trpc.propertyCategories.getCategoryById.queryOptions(
      categoryId ? { id: categoryId } : skipToken,
    ));

  const { data: propertyStep2Data, isPending } = useQuery(trpc.property.getStep2.queryOptions(
    propertyId && agentId ? { id: propertyId, agentId: agentId } : skipToken,
    {
      refetchOnMount: true,
      refetchOnWindowFocus: true,
      staleTime: 0,
    },
  ));

  const { mutate: submitStep2 } = useMutation(trpc.property.submitStep2.mutationOptions());

  const form = useForm({
    defaultValues: {
      propertyAddress: undefined,
      propertyLatitude: 0,
      propertyLongitude: 0,
      propertyGooglePlaceId: "",
      propertyAddressComponents: {},
      propertyMarkersLatLng: [],
      propertyLocation: undefined,
      utilities: [],
    } as PostPropertyStep2Base,
    onSubmit: ({ value }) => {
      if (!agentId || !propertyId) {
        return toast.error("One or both IDs missing (Agent/Property)");
      }

      submitStep2(
        { ...value, agentId: agentId, propertyId: propertyId },
        {
          onSuccess: (opts) => {
            void queryClient.invalidateQueries({
              queryKey: trpc.property.getStep2.queryKey(),
            });
            toast.success(opts.message);
            void goToNextStep();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    },
  });

  const propertyMarkersLatLng = useStore(
    form.store,
    (state) => state.values.propertyMarkersLatLng,
  );

  /**
   * Handles form submission and validation before proceeding to the next step
   * Shows a confirmation dialog if no property markers are set
   *
   * @returns {Promise<void>}
   */
  const handleNextStep = async () => {
    if (propertyMarkersLatLng.length === 0) {
      setOpen(true);
      return;
    }

    await form.handleSubmit();
  };

  /**
   * Updates form values when drawing points change on the map
   * Validates the propertyMarkersLatLng field after update
   */
  useEffect(() => {
    const updateDrawingPoints = async () => {
      if (drawingPoints.length > 0) {
        form.setFieldValue("propertyMarkersLatLng", drawingPoints);
        await form.validateField("propertyMarkersLatLng", "change");
      } else {
        form.resetField("propertyMarkersLatLng");
        await form.validateField("propertyMarkersLatLng", "change");
      }
    };

    void updateDrawingPoints();
  }, [drawingPoints, form]);

  /**
   * Populates form with existing property data when editing
   * Sets drawing points on the map from saved property boundaries
   */
  useEffect(() => {
    if (propertyStep2Data) {
      setIsSettingValues(true);

      form.setFieldValue(
        "propertyAddress",
        propertyStep2Data.propertyAddress ?? undefined,
      );
      form.setFieldValue(
        "propertyLatitude",
        propertyStep2Data.propertyLatitude ?? 0,
      );
      form.setFieldValue(
        "propertyLongitude",
        propertyStep2Data.propertyLongitude ?? 0,
      );
      form.setFieldValue(
        "propertyGooglePlaceId",
        propertyStep2Data.propertyGooglePlaceId ?? "",
      );
      form.setFieldValue(
        "propertyAddressComponents",
        propertyStep2Data.propertyAddressComponents ?? {},
      );
      form.setFieldValue(
        "propertyMarkersLatLng",
        (propertyStep2Data?.propertyMarkersLatLng as Point[]) ?? [],
      );
      form.setFieldValue(
        "propertyLocation",
        propertyStep2Data.propertyLocation ?? undefined,
      );
      form.setFieldValue("utilities", propertyStep2Data?.utilities ?? []);

      setDrawingPoints(
        (propertyStep2Data?.propertyMarkersLatLng as Point[]) ?? [],
      );

      void form.validateAllFields("mount");
      setIsSettingValues(false);
    }
  }, [propertyStep2Data, form]);

  if ((isPending || isSettingValues) && propertyId) {
    return <FormLoading />;
  }

  return (
    <>
      <div className="container mx-auto mb-10 max-w-full">
        <div className="flex justify-between lg:space-y-1.5 xl:space-y-2 2xl:space-y-2.5">
          <div className="space-y-1 lg:space-y-1.5 xl:space-y-2 2xl:space-y-2.5">
            <h2 className="font-airbnb_w_xbd text-xl font-extrabold text-primary-2-700 lg:text-2xl 2xl:text-3xl">
              Location
            </h2>

            <div>
              <p className="font-airbnb_w_bk text-[10px] text-text-550 sm:text-xs md:text-sm lg:text-base 2xl:text-lg">
                Provide location details of your property
              </p>
            </div>
          </div>

          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger>
              <div className="flex flex-col items-end justify-between">
                <MapPinnedIcon className="text-primary-2-750"></MapPinnedIcon>
                <p className="font-airbnb_w_bk text-[10px] text-primary-2-750 sm:text-xs md:text-sm lg:text-base 2xl:text-lg">
                  Pin property on map
                </p>
              </div>
            </DialogTrigger>
            <DialogContent>
              <div className="">
                <div className="flex items-center justify-center gap-4 px-4 py-2">
                  <div className="flex w-full items-center gap-4">
                    <div className="w-full">
                      <GoogleAutocompleteInput
                        className={cn(
                          "w-full rounded-md p-2 outline-none focus:outline-none",
                          disableSearchBar && "cursor-no-drop",
                        )}
                        placeholder="Search Location..."
                        onLocationSelect={(resp) => {
                          setSearchSelectedLocation({
                            address: resp.fullAddress,
                            place_id: resp.placeId,
                            lng: resp.longitude,
                            lat: resp.latitude,
                            address_components: resp.addressComponents,
                          });
                        }}
                        searchBounds={{
                          point1: locationRestriction[0],
                          point2: locationRestriction[1],
                          point3: locationRestriction[2],
                          point4: locationRestriction[3],
                        }}
                        onUserLocationDetect={(e) => {
                          setSearchSelectedLocation(undefined);
                          setUserLocation({
                            lat: e.latitude,
                            lng: e.longitude,
                          });
                        }}
                        showSearchIcon={true}
                        showAutoDetectLocationIcon={true}
                      />
                    </div>
                  </div>

                  <DialogClose
                    onClick={() => {
                      setDisableSearchBar(false);
                      if (drawingPoints.length < 3) setShowAlert(true);
                    }}
                  >
                    <XIcon className="size-6 rounded-full border p-1 text-primary-700" />
                  </DialogClose>
                </div>

                <form.Field name="propertyMarkersLatLng">
                  {(field) => (
                    <DrawableGoogleMaps
                      searchLat={Number(searchSelectedLocation?.lat)}
                      searchLng={Number(searchSelectedLocation?.lng)}
                      userLocation={userLocation}
                      drawingPoints={field.state.value as Point[]}
                      setDrawingPoints={setDrawingPoints}
                      setUserLocation={setUserLocation}
                      setDialogOpen={setOpen}
                      zoomLevel={14}
                      setDisable={setDisableSearchBar}
                    />
                  )}
                </form.Field>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <form className="container mx-auto max-w-full space-y-2">
        {/* property address */}
        <div>
          {selectedCategory?.showPropertyAddress !== "HIDE" && (
            <form.Field
              name="propertyAddress"
              validators={{
                onChange:
                  selectedCategory?.showPropertyAddress === "SHOW"
                    ? PostPropertyStep2BaseSchema.pick({
                        propertyAddress: true,
                      }).shape.propertyAddress.refine(
                        (val) => (!val ? false : true),
                        {
                          message: "Property address is required",
                        },
                      )
                    : selectedCategory?.showPropertyAddress === "OPTIONAL"
                      ? PostPropertyStep2BaseSchema.pick({
                          propertyAddress: true,
                        }).shape.propertyAddress
                      : undefined,
              }}
            >
              {(field) => (
                <>
                  <FormLabel
                    className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                    htmlFor={field.name}
                  >
                    Property Address{" "}
                    {selectedCategory?.showPropertyAddress === "OPTIONAL" && (
                      <span className="ml-1 text-sm text-gray-500">
                        (Optional)
                      </span>
                    )}
                  </FormLabel>
                  <GoogleAutocompleteInput
                    className="mt-2"
                    initialValue={field.state.value}
                    onInputChange={(value) => {
                      const newValue = value === "" ? undefined : value;
                      field.handleChange(newValue);
                    }}
                    placeholder="Property address"
                    onLocationSelect={async (e) => {
                      field.handleChange(e.fullAddress);
                      form.setFieldValue(
                        "propertyLatitude",
                        Number(e.latitude),
                      );
                      form.setFieldValue(
                        "propertyLongitude",
                        Number(e.longitude),
                      );
                      form.setFieldValue("propertyGooglePlaceId", e.placeId);
                      form.setFieldValue(
                        "propertyAddressComponents",
                        e.addressComponents,
                      );

                      await form.validateField("propertyLatitude", "change");
                      await form.validateField("propertyLongitude", "change");
                      await form.validateField(
                        "propertyGooglePlaceId",
                        "change",
                      );
                      await form.validateField(
                        "propertyAddressComponents",
                        "change",
                      );
                    }}
                    searchBounds={{
                      point1: locationRestriction[0],
                      point2: locationRestriction[1],
                      point3: locationRestriction[2],
                      point4: locationRestriction[3],
                    }}
                    showSearchIcon={true}
                  />
                  <FieldError>
                    {field.state.meta.errors.length > 0
                      ? field.state.meta.errorMap.onChange?.map(
                          (item) => item.message,
                        )
                      : null}
                  </FieldError>
                </>
              )}
            </form.Field>
          )}
        </div>

        {/* propertyLocation, Flat, House no., Building, Company, Apartment */}
        <div>
          {selectedCategory?.showPropertyLocation !== "HIDE" && (
            <form.Field
              name="propertyLocation"
              validators={{
                onChange:
                  selectedCategory?.showPropertyLocation === "SHOW"
                    ? PostPropertyStep2BaseSchema.pick({
                        propertyLocation: true,
                      }).shape.propertyLocation.refine((val) => !!val, {
                        message:
                          "Flat, House no., Building, Company, Apartment is required",
                      })
                    : selectedCategory?.showPropertyLocation === "OPTIONAL"
                      ? PostPropertyStep2BaseSchema.pick({
                          propertyLocation: true,
                        }).shape.propertyLocation
                      : undefined,
              }}
            >
              {(field) => (
                <>
                  <FormLabel
                    className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                    htmlFor={field.name}
                  >
                    Property Flat, House no., Building, Company, Apartment{" "}
                    {selectedCategory?.showPropertyLocation === "OPTIONAL" && (
                      <span className="ml-1 text-sm text-gray-500">
                        (Optional)
                      </span>
                    )}
                  </FormLabel>
                  <Input
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    type="text"
                    onChange={(e) => {
                      const value =
                        e.target.value === "" ? undefined : e.target.value;
                      field.handleChange(value);
                    }}
                  />
                  <FieldError>
                    {field.state.meta.errors.length > 0
                      ? field.state.meta.errorMap.onChange?.map(
                          (item) => item.message,
                        )
                      : null}
                  </FieldError>
                </>
              )}
            </form.Field>
          )}
        </div>

        {/* utilities */}
        <div>
          {selectedCategory?.showUtilities !== "HIDE" && (
            <form.Field
              name="utilities"
              listeners={{
                onChange: () => {
                  void form.validateField("utilities", "change");
                },
              }}
              validators={{
                onChange:
                  selectedCategory?.showUtilities === "SHOW"
                    ? z
                        .array(
                          z.object({
                            utility: z
                              .string()
                              .min(1, "Utility name is required"),
                            distanceInKm: z.coerce
                              .number({
                                invalid_type_error: "Must be a number",
                              })
                              .positive({
                                message: "Distance must be greater than 0",
                              }),
                          }),
                        )
                        .min(1, "At least one utility is required")
                    : selectedCategory?.showUtilities === "OPTIONAL"
                      ? z
                          .array(
                            z.object({
                              utility: z
                                .string()
                                .min(1, "Utility name is required"),
                              distanceInKm: z.coerce
                                .number({
                                  invalid_type_error: "Must be a number",
                                })
                                .positive({
                                  message: "Distance must be greater than 0",
                                }),
                            }),
                          )
                          .optional()
                      : undefined,
              }}
            >
              {(utilityField) => (
                <div>
                  <FormLabel
                    className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg 2xl:text-xl"
                    htmlFor={utilityField.name}
                  >
                    Utilities
                    {selectedCategory?.showUtilities === "OPTIONAL" && (
                      <span className="ml-1 text-sm text-gray-500">
                        (Optional)
                      </span>
                    )}
                  </FormLabel>

                  <div className="mt-2 space-y-4">
                    {/* @ts-ignore */}
                    {utilityField.state.value?.map((utility, index) => (
                      <div key={index} className="flex items-end gap-4">
                        <div className="flex-1">
                          <form.Field name={`utilities[${index}].utility`}>
                            {(field) => (
                              <>
                                <FormLabel
                                  htmlFor={field.name}
                                  className="text-sm font-medium"
                                >
                                  Utility Name
                                </FormLabel>
                                <Input
                                  id={field.name}
                                  name={field.name}
                                  // TODO: need to fix this ts issue
                                  // @ts-ignore
                                  value={field.state.value ?? ""}
                                  onChange={(e) =>
                                    field.handleChange(e.target.value)
                                  }
                                  placeholder="E.g., School, Hospital, Market"
                                  className={
                                    field.state.meta.errors.length > 0
                                      ? "border-red-500"
                                      : ""
                                  }
                                />
                                <FieldError>
                                  {field.state.meta.errors.length > 0
                                    ? // TODO: need ts fix here. i dont why the field is not able to recognize the error.
                                      // @ts-ignore
                                      field.state.meta?.errorMap?.onChange?.map(
                                        // @ts-ignore
                                        (error) => error.message,
                                      )
                                    : null}
                                </FieldError>
                              </>
                            )}
                          </form.Field>
                        </div>

                        <div className="flex-1">
                          <form.Field
                            name={`utilities[${index}].distanceInKm`}
                            listeners={{
                              onChange: () => {
                                void form.validateField("utilities", "change");
                              },
                            }}
                          >
                            {(field) => (
                              <>
                                <FormLabel
                                  htmlFor={field.name}
                                  className="text-sm font-medium"
                                >
                                  Distance (km)
                                </FormLabel>
                                <Input
                                  id={field.name}
                                  name={field.name}
                                  type="number"
                                  step="0.1"
                                  //@ts-expect-error - this is a workaround to fix the type error
                                  value={(field.state.value as string) || ""}
                                  onChange={(e) => {
                                    const value =
                                      e.target.value === ""
                                        ? undefined
                                        : Number(e.target.value);
                                    field.handleChange(value);
                                  }}
                                  placeholder="Distance in km"
                                  className={
                                    field.state.meta.errors.length > 0
                                      ? "border-red-500"
                                      : ""
                                  }
                                />
                                <FieldError>
                                  {field.state.meta.errors.length > 0
                                    ? // TODO: need ts fix here. i dont why the field is not able to recognize the error.
                                      // @ts-ignore
                                      field.state.meta.errorMap.onChange?.map(
                                        // @ts-ignore
                                        (error) => error.message,
                                      )
                                    : null}
                                </FieldError>
                              </>
                            )}
                          </form.Field>
                        </div>

                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => {
                            const newValue = [
                              ...(utilityField.state.value ?? []),
                            ];
                            newValue.splice(index, 1);
                            utilityField.handleChange(newValue);
                          }}
                          className="h-10 w-10"
                        >
                          <TrashIcon className="size-4" />
                        </Button>
                      </div>
                    ))}
                  </div>

                  <FieldError>
                    {utilityField.state.meta.errors.length > 0
                      ? utilityField.state.meta.errorMap.onChange?.map(
                          (error) => error.message,
                        )
                      : null}
                  </FieldError>

                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      utilityField.handleChange([
                        ...(utilityField.state.value ?? []),
                        { utility: "", distanceInKm: 0 },
                      ]);
                    }}
                    className="mt-4 w-full border border-primary-2-800 bg-white px-5 py-3.5 font-airbnb_w_md text-base font-medium text-text-600 hover:bg-primary-2-800 hover:text-white"
                  >
                    <PlusIcon className="mr-2 size-4" />
                    Add Utility
                  </Button>
                </div>
              )}
            </form.Field>
          )}
        </div>
      </form>

      <form.Subscribe
        selector={(state) => [state.canSubmit, state.isSubmitting]}
        children={([canSubmit, isSubmitting]) => (
          <PostPropertyFooter
            canSubmit={canSubmit}
            isSubmitting={isSubmitting}
            handleNextStep={handleNextStep}
          />
        )}
      />
    </>
  );
};

export default PostPropertyFormStep2;
