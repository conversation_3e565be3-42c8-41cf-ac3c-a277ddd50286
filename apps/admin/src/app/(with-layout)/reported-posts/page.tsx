"use client";
import React from "react";
import Loading from "~/app/components/loading";
import { useTRPC } from "~/trpc/react";
import PostCard from "@repo/ui/components/shared/post-card";
import { Button } from "@repo/ui/components/ui/button";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { toast } from "@repo/ui/components/ui/sonner";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogClose,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/ui/default-dialog";
import ViewReportingUserDialog from "./view-reporting-user-dialog";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

const ReportedPosts = () => {
  const trpc = useTRPC();
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const removePost = useMutation(trpc.post.deletePost.mutationOptions());
  const activatePost = useMutation(trpc.post.activatePost.mutationOptions());
  const { data: reportedPosts, isLoading } =
    useQuery(trpc.post.getAllReportedPosts.queryOptions());

  const handlePostClick = (postId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("postId", postId);
    router.replace(`?${params}`, { scroll: false });
  };

  const handleRemovePost = (postId: string) => {
    removePost.mutate(
      { id: postId },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void queryClient.invalidateQueries({
            queryKey: trpc.post.getAllReportedPosts.queryKey(),
          });
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  const handleActivatePost = (postId: string) => {
    activatePost.mutate(
      { id: postId },
      {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void queryClient.invalidateQueries({
            queryKey: trpc.post.getAllReportedPosts.queryKey(),
          });
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      },
    );
  };

  return (
    <>
      <div>
        <div className="flex items-center justify-between">
          <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
            Reported Posts
          </h1>
        </div>
        <div className="mx-auto mt-10 flex flex-wrap gap-10">
          {isLoading ? (
            <div className="flex min-h-full items-center justify-center gap-2">
              <Loading className="border-4" />
              <p className="font-medium">Fetching Reported Posts...</p>
            </div>
          ) : reportedPosts && reportedPosts.length > 0 ? (
            reportedPosts.map((item) => {
              return (
                <div className="flex w-[600px] flex-col gap-5">
                  <div className="text-xl">
                    Reported by - {item.reportCount}{" "}
                    {item.reportCount === 1 ? "User" : "Users"}{" "}
                  </div>
                  <PostCard onPostClick={handlePostClick} {...item}></PostCard>
                  <div className="flex gap-5">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          className={`${item.deletedAt ? "bg-green-500" : ""} `}
                        >
                          {item.deletedAt ? "Activate" : "Deactivate"} Post
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>
                            Are you sure you want to{" "}
                            {item.deletedAt ? "Activate" : "Deactivate"} this
                            post ?
                          </DialogTitle>
                          <DialogDescription>
                            This action will{" "}
                            {item.deletedAt
                              ? "make this post visible to the users"
                              : "hide this post from the users"}
                          </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                          <DialogClose asChild>
                            <Button variant={"outline"}>Cancel</Button>
                          </DialogClose>
                          <DialogClose asChild>
                            <Button
                              type="submit"
                              onClick={() => {
                                return item.deletedAt
                                  ? handleActivatePost(item.id)
                                  : handleRemovePost(item.id);
                              }}
                            >
                              Confirm
                            </Button>
                          </DialogClose>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                    <ViewReportingUserDialog
                      item={item}
                    ></ViewReportingUserDialog>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="flex min-h-full items-center justify-center">
              <p>No Reported Posts Found !</p>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ReportedPosts;
