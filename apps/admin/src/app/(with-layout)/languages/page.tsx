'use client';

import { useTRPC } from "~/trpc/react";
import { LanguageDataTable } from "./language-data-table";
import LanguageFormSheet from "./language-form-sheet";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ language_id?: string }>;

const LanguagesPage = () => {
  const trpc = useTRPC();
  const { data: languages } = useQuery(trpc.language.getAll.queryOptions());

  if (!languages) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Languages
      </h1>

      <LanguageFormSheet />
      <LanguageDataTable data={languages} />
    </>
  );
};

export default LanguagesPage;
