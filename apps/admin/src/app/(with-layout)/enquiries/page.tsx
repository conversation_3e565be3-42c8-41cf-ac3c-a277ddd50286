"use client";

import React from "react";
import { useTRPC } from "~/trpc/react";
import EnquiriesDataTable, {
  EnquiriesDataTableColumns,
} from "./enquiries-data-table";
import Loading from "~/app/components/loading";
import { useQuery } from "@tanstack/react-query";

const EnquiriesPage = () => {
  const trpc = useTRPC();
  const { data: enquiries, isLoading } =
    useQuery(trpc.enquiries.getAllEnquiries.queryOptions());

  if (isLoading)
    return (
      <div className="flex min-h-full items-center justify-center gap-2">
        <Loading className="border-4" />
        <p className="font-medium">Fetching Enquiries...</p>
      </div>
    );

  return (
    <>
      <h1 className="font-airbnb_w_md text-3xl font-semibold">Enquiries</h1>
      <EnquiriesDataTable
        data={enquiries ?? []}
        columns={EnquiriesDataTableColumns}
      />
    </>
  );
};

export default EnquiriesPage;
