'use client';

import { useTR<PERSON> } from "~/trpc/react";
import JobRolesFormSheet from "./job-roles-form-sheet";
import JobRolesDataTable, {
  jobRolesDataTableColumns,
} from "./job-roles-data-table";
import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ job_role_id?: string; showDeleted?: string }>;

const JobRolesPage = () => {
  // const searchParams = await props.searchParams;
  const trpc = useTRPC();
  const searchParams = useSearchParams();
  const { data: jobRoles } = useQuery(trpc.jobRole.getAllJobRoles.queryOptions({
    showDeleted: searchParams.get('showDeleted') === "true",
  }));

  // if (searchParams.job_role_id) {
  //   await api.jobRole.getJobRoleById.prefetch({ id: searchParams.job_role_id });
  // }

  if (!jobRoles) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Job Roles
      </h1>

      <JobRolesFormSheet showDeleted={searchParams.get('showDeleted') ?? 'false'} />
      <JobRolesDataTable data={jobRoles} columns={jobRolesDataTableColumns} />
    </>
  );
};

export default JobRolesPage;
