"use client";

import PropertyDetail from "@repo/ui/components/shared/property-detail";
import {
  Dialog,
  DialogContent,
  DialogTitle,
} from "@repo/ui/components/ui/dialog";
import { useTRPC } from "~/trpc/react";
import { useRouter, useSearchParams } from "next/navigation";
import { skipToken, useQuery } from "@tanstack/react-query";

import { useSession } from "next-auth/react";

const PropertyDialogProvider = () => {
  const trpc = useTRPC();
  const { data: session } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const propertyId = searchParams.get("viewPropertyId");

  const { data } = useQuery(trpc.property.getPropertyDetails.queryOptions(
    propertyId ? { id: propertyId } : skipToken,
  ));

  const property = data;

  if (!property) {
    return null;
  }

  const handleClose = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete("viewPropertyId");
    router.push(`?${params}`);
  };

  const userProfileClick = (agentId: string) => {
    const params = new URLSearchParams(searchParams);
    params.set("viewAgentId", agentId);
    router.push(`?${params.toString()}`, { scroll: false });
  };

  return (
    <>
      <Dialog
        open={propertyId ? true : false}
        onOpenChange={(open) => {
          if (!open) handleClose();
        }}
      >
        <DialogContent
          onEscapeKeyDown={() => {
            handleClose();
          }}
        >
          <DialogTitle className="hidden">Property Details</DialogTitle>
          <PropertyDetail
            property={property}
            locationIcon="/location.svg"
            userId={session?.user.id}
            likePropertyButton={<></>}
            sharePropertyButton={<></>}
            userProfileClick={userProfileClick}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PropertyDialogProvider;
