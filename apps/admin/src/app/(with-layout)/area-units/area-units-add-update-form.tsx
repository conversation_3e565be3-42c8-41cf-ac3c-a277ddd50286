"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import AddUpdateAreaUnitSchema from "~/server/api/validations/add-update-area-units.validation";
import { useTRPC } from "~/trpc/react";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import { skipToken, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSheet } from "~/app/hooks/use-sheet";
import { AreaUnitParamName } from "~/app/helpers/constants";
import { MultipleSelector } from "@repo/ui/components/ui/multiple-selector";

const AreaUnitsAddUpdateForm = () => {
  const { paramValue: areaUnitId, closeSheet } = useSheet(AreaUnitParamName);
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const {
    data: areaUnitDetail,
    isLoading,
    error,
  } = useQuery(trpc.areaUnit.getAreaUnitById.queryOptions(
    areaUnitId ? { areaUnitId } : skipToken,
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  ));

  const { data: propertyCategories } =
    useQuery(trpc.propertyCategories.getAllCategories.queryOptions());

  const { mutate: updateAreaUnit, isPending: isPendingUpdateMutation } =
    useMutation(trpc.areaUnit.updateAreaUnit.mutationOptions());
  const { mutate: addAreaUnit, isPending: isPendingAddMutation } =
    useMutation(trpc.areaUnit.addAreaUnit.mutationOptions());

  const form = useForm<z.infer<typeof AddUpdateAreaUnitSchema>>({
    resolver: zodResolver(AddUpdateAreaUnitSchema),
    defaultValues: areaUnitDetail
      ? {
          name: areaUnitDetail.name,
          shortForm: areaUnitDetail.shortForm,
          category: areaUnitDetail.category,
          conversionMultiplyer: areaUnitDetail.conversionMultiplyer,
        }
      : {
          name: "",
          shortForm: "",
          category: [],
          conversionMultiplyer: undefined,
        },
    values: areaUnitDetail
      ? {
          name: areaUnitDetail.name,
          shortForm: areaUnitDetail.shortForm,
          category: areaUnitDetail.category,
          conversionMultiplyer: areaUnitDetail.conversionMultiplyer,
        }
      : undefined,
  });

  const onSubmit = (values: z.infer<typeof AddUpdateAreaUnitSchema>) => {
    if (areaUnitId) {
      updateAreaUnit(
        { ...values, id: areaUnitId },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            void queryClient.invalidateQueries({
              queryKey: [
                trpc.areaUnit.getAreaUnitById.queryKey({
                  areaUnitId,
                }),
                trpc.areaUnit.getAllAreaUnits.queryKey(),
              ],
            });
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      addAreaUnit(values, {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void queryClient.invalidateQueries({
            queryKey: trpc.areaUnit.getAllAreaUnits.queryKey(),
          });
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  if (isLoading && areaUnitId) {
    return <div className="p-4 text-center">Loading Area Unit details...</div>;
  }

  if (error && areaUnitId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading Area Unit: {error.message}
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} noValidate={true} className="w-full space-y-4">
        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-airbnb_w_md text-base font-medium text-text-600 lg:text-lg">
                Category
              </FormLabel>
              <FormControl>
                <MultipleSelector
                  value={field.value.map((value) => ({
                    label:
                      propertyCategories?.find((opt) => opt.id === value.id)
                        ?.name ?? value.name,
                    value: value.id,
                  }))}
                  onChange={(options) => {
                    const selectedCatgories = options.map((opt) => opt.value);
                    field.onChange(
                      propertyCategories?.filter((category) =>
                        selectedCatgories.includes(category.id),
                      ),
                    );
                  }}
                  options={
                    propertyCategories?.map((a) => ({
                      label: a.name,
                      value: a.id,
                    })) ?? []
                  }
                  placeholder="Select categories"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Area Unit Name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="shortForm"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Short Form</FormLabel>
              <FormControl>
                <Input placeholder="Area Unit Short Form" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="conversionMultiplyer"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Conversion Multiplyer (for square meter)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Type here..."
                  min={0}
                  {...field}
                  onChange={(e) => {
                    const value =
                      e.target.value === ""
                        ? undefined
                        : parseFloat(e.target.value);
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {areaUnitId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          areaUnitId && (
            <Button type="submit" className="w-full py-3">
              Update
            </Button>
          )
        )}

        {!areaUnitId && isPendingAddMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !areaUnitId && (
            <Button type="submit" className="w-full py-3">
              Create
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default AreaUnitsAddUpdateForm;
