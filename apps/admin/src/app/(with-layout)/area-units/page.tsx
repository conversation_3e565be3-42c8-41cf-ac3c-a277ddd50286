'use client';
import { useTR<PERSON> } from "~/trpc/react";
import {
  AreaUnitsDataTableColumns,
  AreaUnitsDataTable,
} from "./area-units-data-table";
import AreaUnitsFormSheet from "./area-units-form-sheet";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ area_unit_id?: string }>;

const AreaUnitsPage = () => {
  const trpc = useTRPC();
  const { data: areaUnits } = useQuery(trpc.areaUnit.getAllAreaUnits.queryOptions());

  // if (searchParams.area_unit_id) {
  //   await api.areaUnit.getAreaUnitById.prefetch({
  //     areaUnitId: searchParams.area_unit_id,
  //   });
  // }

  if (!areaUnits) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Area Units
      </h1>

      <AreaUnitsFormSheet />

      <AreaUnitsDataTable
        data={areaUnits}
        columns={AreaUnitsDataTableColumns}
      />
    </>
  );
};

export default AreaUnitsPage;
