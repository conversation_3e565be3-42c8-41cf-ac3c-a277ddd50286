'use client';
import { useTRPC } from "~/trpc/react";
import CitiesDataTable, { CitiesDataTableColumns } from "./citites-data-table";
import CityFormSheet from "./city-form-sheet";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ city_id?: string }>;

const Cities = () => {
  const trpc = useTRPC();
  // const searchParams = await props.searchParams;
  const { data: cities } = useQuery(trpc.city.getAllCities.queryOptions());

  // if (searchParams.city_id) {
  //   await api.city.getCityById.prefetch({ cityId: searchParams.city_id });
  // }

  if (!cities) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">Cities</h1>

      <CityFormSheet />
      <CitiesDataTable data={cities} columns={CitiesDataTableColumns} />
    </>
  );
};

export default Cities;
