"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { Rating } from "@smastrom/react-rating";
import "@smastrom/react-rating/style.css";

import { But<PERSON> } from "@repo/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/ui/form";
import { Input } from "@repo/ui/components/ui/input";
import { toast } from "@repo/ui/components/ui/sonner";
import { useTRPC } from "~/trpc/react";
import { skipToken, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { LoadingButton } from "@repo/ui/components/ui/loading-button";
import addUpdateTestimonialSchema from "~/server/api/validations/add-update-customer-validation";
import { Textarea } from "@repo/ui/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/ui/select";
import { useSheet } from "~/app/hooks/use-sheet";
import { TestimonialParamName } from "~/app/helpers/constants";

const TestimonialAddUpdateForm = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { paramValue: testimonialId, closeSheet } =
    useSheet(TestimonialParamName);

  const {
    data: testimonialDetail,
    isLoading,
    error,
  } = useQuery(
    trpc.customerTestimonails.getCustomerTestimonialById.queryOptions(
      testimonialId ? { id: testimonialId } : skipToken,
      {
        staleTime: 1000 * 60 * 5, // 5 minutes
      },
    )
  );

  const { mutate: updateTestimonial, isPending: isPendingUpdateMutation } =
    useMutation(trpc.customerTestimonails.updateCustomerTestimonials.mutationOptions());
  const { mutate: addTestimonial, isPending: isPendingAddMutation } =
    useMutation(trpc.customerTestimonails.addCustomerTestimonials.mutationOptions());

  const { data: cities, isLoading: isLoadingCities } =
    useQuery(trpc.city.getAllCities.queryOptions(undefined, {
      refetchOnMount: true,
      staleTime: 0,
    }));

  const form = useForm<z.infer<typeof addUpdateTestimonialSchema>>({
    resolver: zodResolver(addUpdateTestimonialSchema),
    defaultValues: testimonialDetail
      ? {
          name: testimonialDetail.name,
          description: testimonialDetail.description,
          rating: testimonialDetail.rating,
          cityId: testimonialDetail.cityId,
          fileKey: testimonialDetail.fileKey ?? undefined,
          filePublicUrl: testimonialDetail.filePublicUrl ?? undefined,
        }
      : {
          name: "",
          description: "",
          rating: 1,
          cityId: undefined,
          fileKey: undefined,
          filePublicUrl: undefined,
        },
  });

  const onSubmit = (values: z.infer<typeof addUpdateTestimonialSchema>) => {
    if (testimonialId) {
      updateTestimonial(
        { ...values, id: testimonialId },
        {
          onSuccess: (opts) => {
            toast.success(opts.message);
            void queryClient.invalidateQueries({
              queryKey: [
                trpc.customerTestimonails.getCustomerTestimonialById.queryKey({
                  id: testimonialId,
                }),
                trpc.customerTestimonails.getTestimonials.queryKey(),
              ],
            });
            closeSheet();
          },
          onError: (opts) => {
            toast.error(opts.message);
          },
        },
      );
    } else {
      addTestimonial(values, {
        onSuccess: (opts) => {
          toast.success(opts.message);
          void queryClient.invalidateQueries({
            queryKey: trpc.customerTestimonails.getTestimonials.queryKey(),
          });
          closeSheet();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      });
    }
  };

  if (isLoading && testimonialId) {
    return (
      <div className="p-4 text-center">Loading Testimonial details...</div>
    );
  }

  if (error && testimonialId) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading Testimonial: {error.message}
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter the name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  rows={5}
                  placeholder="Enter the description"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="cityId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>City</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select city" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {isLoadingCities ? (
                        <div>Loading...</div>
                      ) : (
                        cities?.map((item) => (
                          <SelectItem key={item.id} value={item.id}>
                            {item.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="rating"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rating</FormLabel>
              <FormControl>
                <Rating
                  style={{ maxWidth: 150 }}
                  value={field.value}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {testimonialId && isPendingUpdateMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Updating...
          </LoadingButton>
        ) : (
          testimonialId && (
            <Button type="submit" className="w-full py-3">
              Update
            </Button>
          )
        )}

        {!testimonialId && isPendingAddMutation ? (
          <LoadingButton className="w-full py-3" loading>
            Creating...
          </LoadingButton>
        ) : (
          !testimonialId && (
            <Button type="submit" className="w-full py-3">
              Create
            </Button>
          )
        )}
      </form>
    </Form>
  );
};

export default TestimonialAddUpdateForm;
