'use client';
import { useTRPC } from "~/trpc/react";
import {
  customerTestimonialDataTableColumns,
  default as TestimonialsDataTable,
} from "./customer-testimonials-data-table";
import TestimonialFormSheet from "./customer-testimonials-form-sheet";
import { TestimonialParamName } from "~/app/helpers/constants";
import { useQuery } from "@tanstack/react-query";

// type SearchParams = Promise<{ [TestimonialParamName]?: string }>;

const CustomerTestimonialsPage = () => {
  const trpc = useTRPC();
  // const searchParams = await props.searchParams;
  const { data: testimonials } = useQuery(trpc.customerTestimonails.getTestimonials.queryOptions());

  // if (searchParams[TestimonialParamName]) {
  //   await api.customerTestimonails.getCustomerTestimonialById.prefetch({
  //     id: searchParams[TestimonialParamName],
  //   });
  // }
  // await api.city.getAllCities.prefetch();

  if (!testimonials) {
    return null;
  }

  return (
    <>
      <h1 className="mb-5 font-airbnb_w_md text-3xl font-semibold">
        Customer Testimonials
      </h1>

      <TestimonialFormSheet />
      <TestimonialsDataTable
        data={testimonials}
        columns={customerTestimonialDataTableColumns}
      />
    </>
  );
};

export default CustomerTestimonialsPage;
