{"name": "@repo/customer-api", "version": "0.1.0", "private": true, "type": "module", "main": "./src/index.ts", "types": "./src/index.ts", "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@repo/aws": "*", "@repo/customer-auth": "*", "@repo/database": "*", "@repo/msg91": "*", "@repo/validators": "*", "@t3-oss/env-nextjs": "^0.10.1", "@trpc/server": "^11.2.0", "cloudinary": "^2.6.0", "superjson": "2.2.1", "jose": "6.0.11", "zod": "^3.23.8"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/prettier-config": "*", "@repo/tsconfig": "*", "eslint": "^9.12.0", "prettier": "^3.3.3", "typescript": "^5.8.2"}, "prettier": "@repo/prettier-config"}