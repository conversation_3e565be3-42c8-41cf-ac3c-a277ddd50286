{"name": "mydeer-property-monorepo", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "database:migration:dev": "cd packages/database && yarn prisma migrate dev", "database:format": "cd packages/database && yarn prisma format", "database:migration:prod": "cd packages/database && yarn prisma db push && yarn prisma generate", "start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios"}, "devDependencies": {"@babel/core": "^7.26.9", "@repo/eslint-config": "*", "@repo/prettier-config": "*", "@repo/tsconfig": "*", "@turbo/gen": "^2.4.4", "prettier": "^3.5.3", "turbo": "^2.4.4", "typescript": "^5.8.2"}, "workspaces": ["apps/*", "packages/*", "tooling/*"], "engines": {"node": ">=22"}, "prettier": "@repo/prettier-config", "version": "1.0.0", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "dependencies": {"expo": "~53.0.9", "react": "19.0.0", "react-native": "0.79.3"}}